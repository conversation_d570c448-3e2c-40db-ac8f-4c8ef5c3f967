# -*- coding: utf-8 -*-
"""
Enterprise File Sharing System Configuration
企业级文件共享系统配置文件
"""
import os
from typing import Optional
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基本配置
    APP_NAME: str = "Enterprise File Sharing System"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 数据库配置
    DATABASE_URL: str = "mysql+pymysql://root:123456@localhost:3306/file_share_system"
    DATABASE_ECHO: bool = False
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_PASSWORD: Optional[str] = None
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-change-this-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # 文件存储配置
    UPLOAD_DIR: str = "storage/uploads"
    THUMBNAIL_DIR: str = "storage/thumbnails"
    TEMP_DIR: str = "storage/temp"
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    ALLOWED_EXTENSIONS: list = [
        "jpg", "jpeg", "png", "gif", "bmp", "tiff", "tif",
        "psd", "ai", "eps", "svg", "pdf", "doc", "docx",
        "xls", "xlsx", "ppt", "pptx", "txt", "zip", "rar"
    ]
    
    # 缩略图配置
    THUMBNAIL_SIZE: tuple = (200, 200)
    THUMBNAIL_QUALITY: int = 85
    
    # 安全配置
    CORS_ORIGINS: list = ["*"]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: list = ["*"]
    CORS_ALLOW_HEADERS: list = ["*"]
    
    # 权限配置
    DEFAULT_USER_ROLE: str = "user"
    ADMIN_ROLE: str = "admin"
    READONLY_ROLE: str = "readonly"
    
    # 登录安全配置
    MAX_LOGIN_ATTEMPTS: int = 5
    LOCKOUT_DURATION_MINUTES: int = 30
    
    # 下载配置
    MAX_DOWNLOAD_FILES: int = 10
    MAX_DOWNLOAD_SIZE_MB: int = 500
    DOWNLOAD_SPEED_LIMIT_KB: int = 1024  # 1MB/s
    
    # 加密配置
    ENCRYPTION_ENABLED: bool = True
    ENCRYPTION_TRIGGER_COUNT: int = 3  # 下载3次后开始加密
    PASSWORD_REQUEST_LIMIT: int = 5  # 密码申请次数限制
    
    # 搜索配置
    SEARCH_RESULTS_PER_PAGE: int = 20
    MAX_SEARCH_RESULTS: int = 1000
    
    # 图像识别配置
    IMAGE_FEATURE_EXTRACTION: bool = True
    SIMILARITY_THRESHOLD: float = 0.8
    
    # 监控配置
    ENABLE_MONITORING: bool = True
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    LOG_MAX_SIZE: str = "10MB"
    LOG_BACKUP_COUNT: int = 5
    
    # 网络访问控制
    INTERNAL_NETWORKS: list = ["***********/16", "10.0.0.0/8", "**********/12"]
    EXTERNAL_ACCESS_ENABLED: bool = False
    IP_WHITELIST: list = []
    
    # 限流配置
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW_SECONDS: int = 60
    
    # 备份配置
    BACKUP_ENABLED: bool = True
    BACKUP_INTERVAL_HOURS: int = 24
    BACKUP_RETENTION_DAYS: int = 30
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings


# 数据库URL构建
def get_database_url() -> str:
    """获取数据库连接URL"""
    return settings.DATABASE_URL


# Redis URL构建
def get_redis_url() -> str:
    """获取Redis连接URL"""
    return settings.REDIS_URL


# 文件路径配置
def get_upload_path() -> str:
    """获取上传文件路径"""
    os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
    return settings.UPLOAD_DIR


def get_thumbnail_path() -> str:
    """获取缩略图路径"""
    os.makedirs(settings.THUMBNAIL_DIR, exist_ok=True)
    return settings.THUMBNAIL_DIR


def get_temp_path() -> str:
    """获取临时文件路径"""
    os.makedirs(settings.TEMP_DIR, exist_ok=True)
    return settings.TEMP_DIR
