"""
模型包初始化
导入所有数据模型
"""

# 导入所有模型
from .user import User, UserGroup, UserRole
from .file import File, Folder
from .permission import Permission, PermissionTemplate, AccessControl, PermissionType
from .activity_log import ActivityLog, SystemLog, SecurityAlert, ActionType, TargetType, LogLevel
from .system_config import SystemConfig, SystemStats, Notification

# 导出所有模型
__all__ = [
    # 用户相关
    "User",
    "UserGroup",
    "UserRole",

    # 文件相关
    "File",
    "Folder",

    # 权限相关
    "Permission",
    "PermissionTemplate",
    "AccessControl",
    "PermissionType",

    # 日志相关
    "ActivityLog",
    "SystemLog",
    "SecurityAlert",
    "ActionType",
    "TargetType",
    "LogLevel",

    # 系统配置相关
    "SystemConfig",
    "SystemStats",
    "Notification"
]
