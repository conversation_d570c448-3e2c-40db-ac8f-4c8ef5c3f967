# 企业级文件共享系统

## 📋 项目概述

这是一个功能完整的企业级文件共享系统，支持多用户权限管理、双搜索引擎、内外网访问控制、实时监控等功能。

### 🎯 核心特性

- 🔍 **双搜索引擎**: 文件名快速搜索 + 图像识别搜索
- 👥 **权限管理**: 多级权限控制，内外网访问控制  
- 📁 **文件管理**: 支持JPG/PSD/TIF/AI/EPS等格式
- 🔐 **安全机制**: 文件加密下载，密码申请机制
- 📊 **实时监控**: 用户行为监控，系统状态监控
- 🚫 **访问控制**: 限流、违规禁止、敏感文件监控

### 🛠️ 技术栈

- **后端**: Python + FastAPI
- **数据库**: MySQL + Redis
- **前端**: HTML + CSS + JavaScript (原生)
- **部署**: Windows 直接部署 (无需Docker)

## 🚀 快速开始

### 环境要求

- Python 3.8+
- MySQL 5.7+
- Redis 6.0+
- Windows 10/11

### 1. 安装依赖

```bash
cd file-share-system/backend
pip install -r requirements.txt
```

### 2. 配置数据库

1. 启动 MySQL 服务
2. 创建数据库:
```sql
CREATE DATABASE file_share_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 配置 Redis

1. 启动 Redis 服务
2. 确保 Redis 运行在默认端口 6379

### 4. 配置环境变量

复制环境配置文件:
```bash
cp .env.example .env
```

编辑 `.env` 文件，修改数据库连接信息:
```
DATABASE_URL=mysql+pymysql://root:123456@localhost:3306/file_share_system
```

### 5. 初始化数据库

```bash
python init_db.py
```

### 6. 启动服务

```bash
python run.py
```

### 7. 访问系统

- 前端页面: http://localhost:8000/static/index.html
- API 文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

### 8. 默认账户

- 用户名: `admin`
- 密码: `admin123`

## 📁 项目结构

```
file-share-system/
├── backend/                 # 后端代码
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py         # FastAPI应用入口
│   │   ├── config.py       # 配置文件
│   │   ├── database.py     # 数据库连接
│   │   ├── models/         # 数据模型
│   │   ├── routers/        # API路由
│   │   ├── services/       # 业务逻辑
│   │   ├── utils/          # 工具函数
│   │   └── middleware/     # 中间件
│   ├── requirements.txt    # Python依赖
│   ├── run.py             # 启动脚本
│   └── init_db.py         # 数据库初始化
├── frontend/               # 前端代码
│   └── index.html         # 主页面
├── storage/               # 文件存储目录
│   ├── uploads/          # 上传文件
│   ├── thumbnails/       # 缩略图
│   └── temp/            # 临时文件
├── logs/                 # 日志文件
├── config/              # 配置文件
└── docs/               # 文档
```

## 🔧 开发指南

### API 接口

系统提供完整的 RESTful API 接口:

- **认证接口**: `/api/auth/*`
- **文件管理**: `/api/files/*`
- **用户管理**: `/api/users/*`
- **管理功能**: `/api/admin/*`
- **搜索功能**: `/api/search/*`
- **系统功能**: `/api/system/*`

### 数据库模型

- **用户模型**: 用户信息、角色、权限
- **文件模型**: 文件信息、路径、权限
- **权限模型**: 细粒度权限控制
- **日志模型**: 操作日志、系统日志
- **配置模型**: 系统配置、统计信息

### 中间件

- **认证中间件**: JWT Token 验证
- **日志中间件**: 请求日志记录
- **限流中间件**: 访问频率控制
- **安全中间件**: 安全检查和防护

## 🔒 安全特性

### 文件安全
- 文件下载加密（可配置加密次数）
- 解压密码申请机制
- 敏感文件标记和监控
- 文件病毒扫描

### 访问控制
- IP 白名单机制
- 内外网访问控制
- 访问频率限制
- 用户行为监控

### 数据保护
- 密码加密存储 (bcrypt)
- JWT Token 认证
- HTTPS 强制加密
- SQL 注入防护
- XSS 攻击防护

## 📊 监控和统计

### 实时监控
- 在线用户数量
- 系统资源使用
- 文件操作监控
- 异常行为检测

### 统计分析
- 用户行为统计
- 文件访问热度
- 下载流量统计
- 系统使用报表

## 🔍 搜索引擎

### 文件名搜索
- 基于 MySQL 全文索引
- 支持模糊搜索、正则表达式
- 搜索结果按相关度排序
- 搜索历史记录

### 图像识别搜索
- 使用 OpenCV 进行特征提取
- 支持多种图像格式
- 相似度计算和匹配
- 以图搜图功能

## 🛡️ 权限管理

### 角色权限
- 管理员: 完全权限
- 普通用户: 基础权限
- 只读用户: 查看权限

### 文件权限
- 读取权限
- 写入权限
- 删除权限
- 下载权限
- 上传权限

### 网络权限
- 内网访问控制
- 外网访问控制
- IP 白名单管理

## 📝 日志系统

### 活动日志
- 用户登录/登出
- 文件上传/下载
- 搜索操作
- 权限变更

### 系统日志
- 错误日志
- 性能日志
- 安全告警
- 系统状态

## 🔄 备份和恢复

### 自动备份
- 数据库定期备份
- 文件增量备份
- 配置文件备份

### 灾难恢复
- 数据恢复方案
- 系统回滚机制
- 故障转移策略

## 📞 技术支持

如有问题，请查看:
1. API 文档: http://localhost:8000/docs
2. 系统日志: `logs/app.log`
3. 健康检查: http://localhost:8000/health

## 📄 许可证

本项目为企业内部使用，保证原创无侵权。
