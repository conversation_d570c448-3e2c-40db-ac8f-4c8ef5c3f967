<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业级文件共享系统开发文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .nav {
            background: #2c3e50;
            padding: 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .nav ul {
            list-style: none;
            display: flex;
            flex-wrap: wrap;
        }
        
        .nav li {
            flex: 1;
            min-width: 150px;
        }
        
        .nav a {
            display: block;
            padding: 15px 20px;
            color: white;
            text-decoration: none;
            border-right: 1px solid #34495e;
            transition: background 0.3s;
        }
        
        .nav a:hover {
            background: #34495e;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 50px;
            padding: 30px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        h2 {
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #667eea;
        }
        
        h3 {
            color: #34495e;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
        }
        
        h4 {
            color: #555;
            font-size: 1.2em;
            margin: 20px 0 10px 0;
        }
        
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .tech-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .tech-item strong {
            color: #2c3e50;
            display: block;
            margin-bottom: 10px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-item {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .architecture {
            background: #2c3e50;
            color: white;
            padding: 30px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            white-space: pre;
            margin: 20px 0;
        }
        
        .sql-code {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 15px 0;
            border-left: 4px solid #007acc;
        }
        
        .directory-tree {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        ul, ol {
            margin: 15px 0;
            padding-left: 30px;
        }
        
        li {
            margin: 8px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
            margin: 5px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 30px;
        }
        
        @media (max-width: 768px) {
            .nav ul {
                flex-direction: column;
            }
            
            .nav a {
                border-right: none;
                border-bottom: 1px solid #34495e;
            }
            
            .content {
                padding: 20px;
            }
            
            .tech-stack,
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1>🗂️ 企业级文件共享系统</h1>
            <p>开发文档 v1.0</p>
        </header>

        <!-- 导航 -->
        <nav class="nav">
            <ul>
                <li><a href="#overview">项目概述</a></li>
                <li><a href="#architecture">系统架构</a></li>
                <li><a href="#database">数据库设计</a></li>
                <li><a href="#features">核心功能</a></li>
                <li><a href="#security">安全机制</a></li>
                <li><a href="#deployment">部署维护</a></li>
            </ul>
        </nav>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 项目概述 -->
            <section id="overview" class="section">
                <h2>📋 项目概述</h2>
                
                <h3>项目目标</h3>
                <p>开发一个企业级文件共享系统，支持多用户权限管理、双搜索引擎、内外网访问控制、实时监控等功能。系统要求稳定流畅、搜索快速、数据安全，并提供完整的源代码。</p>
                
                <h3>技术栈</h3>
                <div class="tech-stack">
                    <div class="tech-item">
                        <strong>🔧 后端框架</strong>
                        Python + FastAPI<br>
                        高性能异步Web框架
                    </div>
                    <div class="tech-item">
                        <strong>🗄️ 数据存储</strong>
                        MySQL (主数据库)<br>
                        Redis (缓存系统)
                    </div>
                    <div class="tech-item">
                        <strong>🎨 前端技术</strong>
                        HTML + CSS + JavaScript<br>
                        原生技术，无复杂框架
                    </div>
                    <div class="tech-item">
                        <strong>🔍 搜索引擎</strong>
                        文件名搜索 + 图像识别<br>
                        双引擎并行工作
                    </div>
                    <div class="tech-item">
                        <strong>🚀 部署环境</strong>
                        Windows 直接部署<br>
                        无需Docker容器
                    </div>
                    <div class="tech-item">
                        <strong>🔒 安全机制</strong>
                        文件加密 + 权限控制<br>
                        多层安全防护
                    </div>
                </div>
                
                <h3>核心功能</h3>
                <div class="features">
                    <div class="feature-item">
                        <h4>📁 文件管理</h4>
                        上传/下载/预览<br>
                        批量操作/文件夹管理
                    </div>
                    <div class="feature-item">
                        <h4>👥 权限控制</h4>
                        多级权限/用户组管理<br>
                        内外网访问控制
                    </div>
                    <div class="feature-item">
                        <h4>🔍 双搜索引擎</h4>
                        文件名快速搜索<br>
                        图像识别搜索
                    </div>
                    <div class="feature-item">
                        <h4>📊 实时监控</h4>
                        用户行为监控<br>
                        系统状态监控
                    </div>
                    <div class="feature-item">
                        <h4>🔐 安全机制</h4>
                        文件加密下载<br>
                        敏感文件监控
                    </div>
                    <div class="feature-item">
                        <h4>📈 统计分析</h4>
                        使用情况统计<br>
                        行为分析报表
                    </div>
                </div>
            </section>

            <!-- 系统架构 -->
            <section id="architecture" class="section">
                <h2>🏗️ 系统架构</h2>
                
                <h3>整体架构图</h3>
                <div class="architecture">┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   FastAPI后端   │    │   MySQL数据库   │
│   (HTML/JS)     │◄──►│   (Python)      │◄──►│   (主数据存储)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                         │
                              ▼                         ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Redis缓存     │    │   文件存储      │
                       │   (会话/缓存)   │    │   (本地磁盘)    │
                       └─────────────────┘    └─────────────────┘</div>
                
                <h3>项目目录结构</h3>
                <div class="directory-tree">file-share-system/
├── backend/                 # 后端代码
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py         # FastAPI应用入口
│   │   ├── config.py       # 配置文件
│   │   ├── database.py     # 数据库连接
│   │   ├── models/         # 数据模型
│   │   ├── routers/        # API路由
│   │   ├── services/       # 业务逻辑
│   │   ├── utils/          # 工具函数
│   │   └── middleware/     # 中间件
│   ├── requirements.txt    # Python依赖
│   └── run.py             # 启动脚本
├── frontend/               # 前端代码
│   ├── static/
│   │   ├── css/
│   │   ├── js/
│   │   └── images/
│   └── templates/
├── storage/               # 文件存储目录
│   ├── uploads/          # 上传文件
│   ├── thumbnails/       # 缩略图
│   └── temp/            # 临时文件
├── logs/                 # 日志文件
├── config/              # 配置文件
└── docs/               # 文档</div>
                
                <div class="highlight">
                    <strong>🎯 架构特点：</strong>
                    <ul>
                        <li>前后端分离，便于维护和扩展</li>
                        <li>数据库+缓存双存储，提升性能</li>
                        <li>模块化设计，代码结构清晰</li>
                        <li>支持水平扩展和负载均衡</li>
                    </ul>
                </div>
            </section>

            <!-- 数据库设计 -->
            <section id="database" class="section">
                <h2>🗄️ 数据库设计</h2>
                
                <h3>用户表 (users)</h3>
                <div class="sql-code">CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    role ENUM('admin', 'user', 'readonly') DEFAULT 'user',
    group_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL
);</div>
                
                <h3>文件表 (files)</h3>
                <div class="sql-code">CREATE TABLE files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(50),
    mime_type VARCHAR(100),
    md5_hash VARCHAR(32),
    thumbnail_path VARCHAR(500),
    folder_id INT,
    uploaded_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT FALSE,
    INDEX idx_filename (filename),
    INDEX idx_file_type (file_type),
    INDEX idx_folder_id (folder_id)
);</div>
                
                <h3>权限表 (permissions)</h3>
                <div class="sql-code">CREATE TABLE permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    file_id INT,
    folder_id INT,
    permission_type ENUM('read', 'write', 'delete', 'download', 'upload') NOT NULL,
    granted_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_file (user_id, file_id),
    INDEX idx_user_folder (user_id, folder_id)
);</div>
                
                <h3>操作日志表 (activity_logs)</h3>
                <div class="sql-code">CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action_type ENUM('login', 'logout', 'upload', 'download', 'delete', 'search', 'view') NOT NULL,
    target_type ENUM('file', 'folder', 'system') NOT NULL,
    target_id INT,
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_action (user_id, action_type),
    INDEX idx_created_at (created_at)
);</div>
                
                <div class="info">
                    <strong>💡 数据库优化要点：</strong>
                    <ul>
                        <li>合理设置索引，提升查询性能</li>
                        <li>使用JSON字段存储复杂数据</li>
                        <li>软删除机制，保护重要数据</li>
                        <li>时间戳自动更新，便于追踪</li>
                    </ul>
                </div>
            </section>

            <!-- 核心功能 -->
            <section id="features" class="section">
                <h2>⚙️ 核心功能实现</h2>

                <h3>权限管理系统</h3>
                <div class="highlight">
                    <strong>🔐 多层权限控制：</strong>
                    <ul>
                        <li><strong>角色权限</strong>：管理员、普通用户、只读用户</li>
                        <li><strong>文件权限</strong>：读取、写入、删除、下载、上传</li>
                        <li><strong>网络权限</strong>：内网访问、外网访问</li>
                        <li><strong>权限继承</strong>：文件夹权限自动继承给子文件</li>
                    </ul>
                </div>

                <h3>双搜索引擎</h3>
                <div class="tech-stack">
                    <div class="tech-item">
                        <strong>📝 文件名搜索引擎</strong>
                        <ul>
                            <li>基于MySQL全文索引</li>
                            <li>支持模糊搜索、正则表达式</li>
                            <li>搜索结果按相关度排序</li>
                            <li>搜索历史记录</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <strong>🖼️ 图像识别搜索引擎</strong>
                        <ul>
                            <li>使用OpenCV进行图像特征提取</li>
                            <li>支持JPG, PNG, TIF, PSD, AI, EPS</li>
                            <li>特征向量存储在数据库中</li>
                            <li>相似度计算和匹配</li>
                        </ul>
                    </div>
                </div>

                <h3>文件管理功能</h3>
                <div class="features">
                    <div class="feature-item">
                        <h4>📤 文件上传</h4>
                        单文件、多文件、拖拽上传<br>
                        大文件分片、断点续传
                    </div>
                    <div class="feature-item">
                        <h4>📥 文件下载</h4>
                        单文件下载、批量下载<br>
                        文件夹打包下载
                    </div>
                    <div class="feature-item">
                        <h4>👁️ 文件预览</h4>
                        图片预览、缩略图生成<br>
                        多种视图模式
                    </div>
                    <div class="feature-item">
                        <h4>🔄 版本控制</h4>
                        文件版本管理<br>
                        历史版本回滚
                    </div>
                </div>
            </section>

            <!-- 安全机制 -->
            <section id="security" class="section">
                <h2>🔒 安全机制</h2>

                <h3>文件安全</h3>
                <div class="warning">
                    <strong>⚠️ 重要安全特性：</strong>
                    <ul>
                        <li><strong>文件加密</strong>：下载文件自动加密，可配置加密触发次数</li>
                        <li><strong>密码申请</strong>：解压密码申请机制，申请次数可控</li>
                        <li><strong>敏感文件</strong>：敏感文件标记，搜索下载重点监控</li>
                        <li><strong>病毒扫描</strong>：上传文件自动病毒扫描</li>
                    </ul>
                </div>

                <h3>访问控制</h3>
                <div class="tech-stack">
                    <div class="tech-item">
                        <strong>🌐 网络访问控制</strong>
                        <ul>
                            <li>内网访问：默认允许内网IP段</li>
                            <li>外网访问：管理员可控制开启/关闭</li>
                            <li>IP白名单：支持IP白名单机制</li>
                            <li>动态权限：不同文件夹可设置不同网络权限</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <strong>🚫 防护机制</strong>
                        <ul>
                            <li>访问频率限制：防止恶意刷取</li>
                            <li>登录失败锁定：多次失败自动锁定</li>
                            <li>违规行为禁止：自动禁止违规用户</li>
                            <li>实时监控告警：异常行为实时告警</li>
                        </ul>
                    </div>
                </div>

                <h3>数据保护</h3>
                <div class="info">
                    <strong>🛡️ 数据安全保障：</strong>
                    <ul>
                        <li>密码加密存储（bcrypt算法）</li>
                        <li>JWT Token认证机制</li>
                        <li>HTTPS强制加密传输</li>
                        <li>SQL注入和XSS防护</li>
                        <li>CSRF攻击防护</li>
                    </ul>
                </div>
            </section>

            <!-- 部署维护 -->
            <section id="deployment" class="section">
                <h2>🚀 部署和维护</h2>

                <h3>Windows部署方案</h3>
                <div class="directory-tree">部署步骤：
1. Python环境配置（Python 3.8+）
2. MySQL数据库安装和配置
3. Redis服务安装和配置
4. 系统服务注册
5. 防火墙规则配置
6. 启动脚本配置
7. 自动备份脚本设置</div>

                <h3>系统监控</h3>
                <div class="features">
                    <div class="feature-item">
                        <h4>📊 实时监控</h4>
                        在线用户数量<br>
                        系统资源使用
                    </div>
                    <div class="feature-item">
                        <h4>📈 统计分析</h4>
                        用户行为统计<br>
                        文件访问热度
                    </div>
                    <div class="feature-item">
                        <h4>🔍 日志管理</h4>
                        操作日志记录<br>
                        异常日志分析
                    </div>
                    <div class="feature-item">
                        <h4>🔧 远程管理</h4>
                        Web管理界面<br>
                        远程配置更新
                    </div>
                </div>

                <h3>系统扩展性</h3>
                <div class="highlight">
                    <strong>🔄 扩展规划：</strong>
                    <ul>
                        <li><strong>水平扩展</strong>：数据库读写分离、Redis集群、负载均衡</li>
                        <li><strong>功能扩展</strong>：在线编辑、移动端适配、API开放</li>
                        <li><strong>性能优化</strong>：缓存策略、索引优化、传输优化</li>
                        <li><strong>安全增强</strong>：多因子认证、审计日志、合规检查</li>
                    </ul>
                </div>

                <div class="warning">
                    <strong>⚠️ 重要提醒：</strong>
                    <ul>
                        <li>软件不允许对共享文件及文件夹做任何变化</li>
                        <li>生成的数据请存储到指定文件夹，保持原文件清洁</li>
                        <li>定期备份数据库和重要配置文件</li>
                        <li>监控系统资源使用，及时扩容</li>
                    </ul>
                </div>
            </section>
        </div>

        <!-- 页脚 -->
        <footer class="footer">
            <p>&copy; 2024 企业级文件共享系统开发文档 | 技术栈：Python + FastAPI + MySQL | 要求：快准稳简洁</p>
        </footer>
    </div>

    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // 导航高亮
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.nav a');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (scrollY >= (sectionTop - 200)) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
