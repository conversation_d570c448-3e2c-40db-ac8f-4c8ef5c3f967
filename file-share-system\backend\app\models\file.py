"""
文件模型
"""
from sqlalchemy import Column, Integer, String, BigInteger, Boolean, DateTime, Text, Float, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base


class File(Base):
    """文件模型"""
    __tablename__ = "files"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 文件基本信息
    filename = Column(String(255), nullable=False, index=True, comment="文件名")
    original_name = Column(String(255), nullable=False, comment="原始文件名")
    file_path = Column(String(500), nullable=False, comment="文件路径")
    file_size = Column(BigInteger, nullable=False, comment="文件大小（字节）")
    
    # 文件类型信息
    file_type = Column(String(50), nullable=True, index=True, comment="文件类型")
    mime_type = Column(String(100), nullable=True, comment="MIME类型")
    file_extension = Column(String(10), nullable=True, index=True, comment="文件扩展名")
    
    # 文件校验
    md5_hash = Column(String(32), nullable=True, index=True, comment="MD5哈希值")
    sha256_hash = Column(String(64), nullable=True, comment="SHA256哈希值")
    
    # 缩略图和预览
    thumbnail_path = Column(String(500), nullable=True, comment="缩略图路径")
    preview_path = Column(String(500), nullable=True, comment="预览图路径")
    has_thumbnail = Column(Boolean, default=False, nullable=False, comment="是否有缩略图")
    
    # 图像信息（如果是图片）
    image_width = Column(Integer, nullable=True, comment="图像宽度")
    image_height = Column(Integer, nullable=True, comment="图像高度")
    image_format = Column(String(20), nullable=True, comment="图像格式")
    
    # 图像特征向量（用于图像搜索）
    feature_vector = Column(Text, nullable=True, comment="图像特征向量JSON")
    color_histogram = Column(Text, nullable=True, comment="颜色直方图JSON")
    
    # 文件夹关联
    folder_id = Column(Integer, ForeignKey("folders.id"), nullable=True, index=True, comment="所属文件夹ID")
    folder_path = Column(String(1000), nullable=True, comment="文件夹路径")
    
    # 上传信息
    uploaded_by = Column(Integer, ForeignKey("users.id"), nullable=False, index=True, comment="上传者ID")
    upload_ip = Column(String(45), nullable=True, comment="上传IP地址")
    
    # 版本信息
    version = Column(Integer, default=1, nullable=False, comment="文件版本")
    parent_file_id = Column(Integer, ForeignKey("files.id"), nullable=True, comment="父文件ID（版本控制）")
    
    # 状态信息
    is_deleted = Column(Boolean, default=False, nullable=False, index=True, comment="是否已删除")
    is_public = Column(Boolean, default=False, nullable=False, comment="是否公开")
    is_encrypted = Column(Boolean, default=False, nullable=False, comment="是否加密")
    is_sensitive = Column(Boolean, default=False, nullable=False, comment="是否敏感文件")
    
    # 访问统计
    download_count = Column(Integer, default=0, nullable=False, comment="下载次数")
    view_count = Column(Integer, default=0, nullable=False, comment="查看次数")
    last_accessed = Column(DateTime, nullable=True, comment="最后访问时间")
    
    # 网络访问权限
    internal_access = Column(Boolean, default=True, nullable=False, comment="内网访问")
    external_access = Column(Boolean, default=False, nullable=False, comment="外网访问")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, index=True, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    
    # 备注和标签
    description = Column(Text, nullable=True, comment="文件描述")
    tags = Column(Text, nullable=True, comment="文件标签JSON")
    
    # 关联关系
    uploader = relationship("User", foreign_keys=[uploaded_by])
    folder = relationship("Folder", back_populates="files")
    
    def __repr__(self):
        return f"<File(id={self.id}, filename='{self.filename}', size={self.file_size})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "filename": self.filename,
            "original_name": self.original_name,
            "file_size": self.file_size,
            "file_type": self.file_type,
            "mime_type": self.mime_type,
            "file_extension": self.file_extension,
            "has_thumbnail": self.has_thumbnail,
            "thumbnail_path": self.thumbnail_path,
            "image_width": self.image_width,
            "image_height": self.image_height,
            "folder_id": self.folder_id,
            "folder_path": self.folder_path,
            "uploaded_by": self.uploaded_by,
            "version": self.version,
            "is_public": self.is_public,
            "is_encrypted": self.is_encrypted,
            "is_sensitive": self.is_sensitive,
            "download_count": self.download_count,
            "view_count": self.view_count,
            "internal_access": self.internal_access,
            "external_access": self.external_access,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_accessed": self.last_accessed.isoformat() if self.last_accessed else None,
            "description": self.description
        }
    
    def get_file_size_mb(self) -> float:
        """获取文件大小（MB）"""
        return round(self.file_size / (1024 * 1024), 2)
    
    def is_image(self) -> bool:
        """检查是否为图片文件"""
        image_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'tif', 'webp']
        return self.file_extension.lower() in image_extensions if self.file_extension else False
    
    def is_document(self) -> bool:
        """检查是否为文档文件"""
        doc_extensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
        return self.file_extension.lower() in doc_extensions if self.file_extension else False
    
    def can_generate_thumbnail(self) -> bool:
        """检查是否可以生成缩略图"""
        thumbnail_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'tif', 'webp', 'pdf']
        return self.file_extension.lower() in thumbnail_extensions if self.file_extension else False


class Folder(Base):
    """文件夹模型"""
    __tablename__ = "folders"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(255), nullable=False, comment="文件夹名称")
    path = Column(String(1000), nullable=False, index=True, comment="文件夹路径")
    
    # 层级关系
    parent_id = Column(Integer, ForeignKey("folders.id"), nullable=True, index=True, comment="父文件夹ID")
    level = Column(Integer, default=0, nullable=False, comment="层级深度")
    
    # 创建信息
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False, comment="创建者ID")
    
    # 状态信息
    is_deleted = Column(Boolean, default=False, nullable=False, comment="是否已删除")
    is_public = Column(Boolean, default=False, nullable=False, comment="是否公开")
    
    # 网络访问权限
    internal_access = Column(Boolean, default=True, nullable=False, comment="内网访问")
    external_access = Column(Boolean, default=False, nullable=False, comment="外网访问")
    
    # 统计信息
    file_count = Column(Integer, default=0, nullable=False, comment="文件数量")
    total_size = Column(BigInteger, default=0, nullable=False, comment="总大小")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    
    # 描述
    description = Column(Text, nullable=True, comment="文件夹描述")
    
    # 关联关系
    files = relationship("File", back_populates="folder")
    creator = relationship("User", foreign_keys=[created_by])
    
    def __repr__(self):
        return f"<Folder(id={self.id}, name='{self.name}', path='{self.path}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "path": self.path,
            "parent_id": self.parent_id,
            "level": self.level,
            "created_by": self.created_by,
            "is_public": self.is_public,
            "internal_access": self.internal_access,
            "external_access": self.external_access,
            "file_count": self.file_count,
            "total_size": self.total_size,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "description": self.description
        }
