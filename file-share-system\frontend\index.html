<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业级文件共享系统</title>
    <link rel="stylesheet" href="static/css/main.css">
    <link rel="stylesheet" href="static/css/components.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <span class="logo">🗂️</span>
                <span class="brand-text">企业级文件共享系统</span>
            </div>

            <!-- 搜索栏 -->
            <div class="search-container">
                <div class="search-engine-toggle">
                    <button id="filename-search" class="search-toggle active">文件名搜索</button>
                    <button id="image-search" class="search-toggle">图像搜索</button>
                </div>
                <div class="search-input-group">
                    <input type="text" id="search-input" placeholder="搜索文件..." />
                    <button id="search-btn" class="search-btn">🔍</button>
                </div>
            </div>

            <!-- 用户菜单 -->
            <div class="user-menu">
                <div class="user-info" id="user-info">
                    <span class="user-avatar">👤</span>
                    <span class="user-name">加载中...</span>
                </div>
                <div class="user-dropdown" id="user-dropdown">
                    <a href="#" id="profile-btn">个人资料</a>
                    <a href="#" id="logout-btn">退出登录</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 通知横幅 -->
    <div class="notification-banner" id="notification-banner">
        <div class="notification-content">
            <span class="notification-text">欢迎使用企业级文件共享系统！支持双搜索引擎、权限管理、实时监控等功能。</span>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-section">
                <h3>📁 文件夹</h3>
                <div class="folder-tree" id="folder-tree">
                    <div class="folder-item active" data-folder-id="0">
                        <span class="folder-icon">📂</span>
                        <span class="folder-name">根目录</span>
                    </div>
                </div>
            </div>

            <div class="sidebar-section">
                <h3>📊 统计信息</h3>
                <div class="stats-panel">
                    <div class="stat-item">
                        <span class="stat-label">总文件数</span>
                        <span class="stat-value" id="total-files">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">在线用户</span>
                        <span class="stat-value" id="online-users">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">存储使用</span>
                        <span class="stat-value" id="storage-used">0 MB</span>
                    </div>
                </div>
            </div>
        </aside>

        <!-- 主内容区 -->
        <main class="content-area">
            <!-- 工具栏 -->
            <div class="toolbar">
                <div class="toolbar-left">
                    <button class="btn btn-primary" id="upload-btn">
                        <span class="btn-icon">📤</span>
                        上传文件
                    </button>
                    <button class="btn btn-secondary" id="new-folder-btn">
                        <span class="btn-icon">📁</span>
                        新建文件夹
                    </button>
                </div>

                <div class="toolbar-right">
                    <div class="view-toggle">
                        <button class="view-btn active" data-view="grid">
                            <span class="view-icon">⊞</span>
                        </button>
                        <button class="view-btn" data-view="list">
                            <span class="view-icon">☰</span>
                        </button>
                    </div>

                    <div class="sort-dropdown">
                        <select id="sort-select">
                            <option value="name">按名称排序</option>
                            <option value="date">按日期排序</option>
                            <option value="size">按大小排序</option>
                            <option value="type">按类型排序</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 面包屑导航 -->
            <div class="breadcrumb" id="breadcrumb">
                <span class="breadcrumb-item active">根目录</span>
            </div>

            <!-- 文件列表区域 -->
            <div class="file-area">
                <div class="file-grid" id="file-grid">
                    <!-- 文件项将通过JavaScript动态加载 -->
                </div>

                <!-- 加载状态 -->
                <div class="loading-state" id="loading-state" style="display: none;">
                    <div class="loading-spinner"></div>
                    <p>正在加载文件...</p>
                </div>

                <!-- 空状态 -->
                <div class="empty-state" id="empty-state" style="display: none;">
                    <div class="empty-icon">📂</div>
                    <h3>文件夹为空</h3>
                    <p>拖拽文件到这里或点击上传按钮添加文件</p>
                </div>
            </div>
        </main>
    </div>

    <!-- 模态框容器 -->
    <div id="modal-container"></div>

    <!-- JavaScript文件 -->
    <script src="static/js/utils.js"></script>
    <script src="static/js/ui-components.js"></script>
    <script src="static/js/api.js"></script>
    <script src="static/js/auth.js"></script>
    <script src="static/js/file-manager.js"></script>
    <script src="static/js/search.js"></script>
    <script src="static/js/main.js"></script>
</body>
</html>
