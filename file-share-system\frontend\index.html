<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业级文件共享系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }

        .logo {
            font-size: 3em;
            margin-bottom: 20px;
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2em;
        }

        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }

        .status.loading {
            background: #fff3cd;
            color: #856404;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
        }

        .info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: left;
        }

        .info h3 {
            color: #495057;
            margin-bottom: 10px;
        }

        .info ul {
            list-style: none;
            padding: 0;
        }

        .info li {
            padding: 5px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .info li:last-child {
            border-bottom: none;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🗂️</div>
        <h1>企业级文件共享系统</h1>

        <div id="status" class="status loading">
            正在检查系统状态...
        </div>

        <div class="info">
            <h3>📋 系统信息</h3>
            <ul>
                <li><strong>版本:</strong> v1.0.0</li>
                <li><strong>技术栈:</strong> Python + FastAPI + MySQL</li>
                <li><strong>特性:</strong> 双搜索引擎、权限管理、实时监控</li>
                <li><strong>部署:</strong> Windows 直接部署</li>
            </ul>
        </div>

        <div class="info">
            <h3>🚀 快速开始</h3>
            <ul>
                <li>1. 确保 MySQL 和 Redis 服务已启动</li>
                <li>2. 运行数据库初始化: <code>python init_db.py</code></li>
                <li>3. 启动后端服务: <code>python run.py</code></li>
                <li>4. 访问 API 文档: <a href="http://localhost:8000/docs" target="_blank">http://localhost:8000/docs</a></li>
            </ul>
        </div>

        <div id="actions" style="display: none;">
            <a href="http://localhost:8000/docs" class="btn" target="_blank">📚 API 文档</a>
            <a href="http://localhost:8000/health" class="btn" target="_blank">🔍 健康检查</a>
        </div>
    </div>

    <script>
        // 检查后端服务状态
        async function checkBackendStatus() {
            const statusEl = document.getElementById('status');
            const actionsEl = document.getElementById('actions');

            try {
                const response = await fetch('http://localhost:8000/health');
                const data = await response.json();

                if (data.status === 'healthy') {
                    statusEl.className = 'status success';
                    statusEl.innerHTML = '✅ 系统运行正常';
                    actionsEl.style.display = 'block';
                } else {
                    statusEl.className = 'status error';
                    statusEl.innerHTML = '⚠️ 系统部分服务异常';
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.innerHTML = '❌ 无法连接到后端服务<br><small>请确保后端服务已启动 (python run.py)</small>';
            }
        }

        // 页面加载时检查状态
        window.addEventListener('load', () => {
            setTimeout(checkBackendStatus, 1000);
        });

        // 每30秒检查一次状态
        setInterval(checkBackendStatus, 30000);
    </script>
</body>
</html>
