"""
系统功能路由
"""
from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session

from app.database import get_db, check_database_health, check_redis_health
from app.middleware.auth import get_current_user_id

router = APIRouter()


@router.get("/health")
async def system_health():
    """系统健康检查"""
    db_status = check_database_health()
    redis_status = await check_redis_health()
    
    return {
        "status": "healthy" if db_status and redis_status else "unhealthy",
        "database": "connected" if db_status else "disconnected",
        "redis": "connected" if redis_status else "disconnected"
    }


@router.get("/stats")
async def system_statistics(
    request: Request,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
):
    """系统统计信息"""
    # TODO: 实现系统统计逻辑
    return {"message": "系统统计功能待实现"}
