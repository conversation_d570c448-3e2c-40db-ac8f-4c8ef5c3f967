/* 主样式文件 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
    background: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 70px;
    gap: 20px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: bold;
    font-size: 1.2em;
    flex-shrink: 0;
    min-width: 200px;
}

.logo {
    font-size: 1.5em;
}

.brand-text {
    white-space: nowrap;
}

/* 搜索栏样式 */
.search-container {
    flex: 1;
    max-width: 500px;
    min-width: 300px;
    position: relative;
}

.search-engine-toggle {
    display: flex;
    margin-bottom: 6px;
    gap: 4px;
    justify-content: center;
}

.search-toggle {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    padding: 3px 10px;
    border-radius: 12px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.3s;
    white-space: nowrap;
}

.search-toggle.active {
    background: rgba(255,255,255,0.3);
    font-weight: bold;
}

.search-input-group {
    display: flex;
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    height: 40px;
}

#search-input {
    flex: 1;
    border: none;
    padding: 0 16px;
    font-size: 14px;
    outline: none;
}

.search-btn {
    background: #667eea;
    border: none;
    color: white;
    padding: 0 16px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-btn:hover {
    background: #5a6fd8;
}

/* 用户菜单样式 */
.user-menu {
    position: relative;
    flex-shrink: 0;
    min-width: 120px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 20px;
    transition: background 0.3s;
    white-space: nowrap;
    font-size: 14px;
}

.user-info:hover {
    background: rgba(255,255,255,0.1);
}

.user-avatar {
    font-size: 1.2em;
}

.user-name {
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    min-width: 150px;
    display: none;
    z-index: 1001;
    margin-top: 5px;
}

.user-dropdown a {
    display: block;
    padding: 12px 16px;
    color: #333;
    text-decoration: none;
    transition: background 0.3s;
    font-size: 14px;
}

.user-dropdown a:hover {
    background: #f8f9fa;
}

/* 通知横幅 */
.notification-banner {
    background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
    color: white;
    padding: 6px 0;
    text-align: center;
    font-size: 13px;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.notification-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 主容器布局 */
.main-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    min-height: calc(100vh - 100px);
}

/* 侧边栏样式 */
.sidebar {
    width: 280px;
    background: white;
    border-right: 1px solid #e0e0e0;
    padding: 20px;
}

.sidebar-section {
    margin-bottom: 30px;
}

.sidebar-section h3 {
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 文件夹树样式 */
.folder-tree {
    list-style: none;
}

.folder-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
    margin-bottom: 2px;
}

.folder-item:hover {
    background: #f0f0f0;
}

.folder-item.active {
    background: #e3f2fd;
    color: #1976d2;
}

.folder-icon {
    font-size: 16px;
}

.folder-name {
    font-size: 14px;
}

/* 统计面板样式 */
.stats-panel {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-size: 13px;
    color: #666;
}

.stat-value {
    font-weight: bold;
    color: #333;
}

/* 主内容区样式 */
.content-area {
    flex: 1;
    padding: 20px;
    background: white;
}

/* 工具栏样式 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.toolbar-left {
    display: flex;
    gap: 10px;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.btn-icon {
    font-size: 16px;
}

/* 视图切换按钮 */
.view-toggle {
    display: flex;
    background: #f8f9fa;
    border-radius: 6px;
    overflow: hidden;
}

.view-btn {
    background: none;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    transition: background 0.3s;
}

.view-btn.active {
    background: #667eea;
    color: white;
}

.view-icon {
    font-size: 16px;
}

/* 排序下拉框 */
#sort-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    cursor: pointer;
}

/* 面包屑导航 */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
    font-size: 14px;
}

.breadcrumb-item {
    color: #666;
    cursor: pointer;
    transition: color 0.3s;
}

.breadcrumb-item.active {
    color: #333;
    font-weight: 500;
}

.breadcrumb-item:hover:not(.active) {
    color: #667eea;
}

/* 文件区域样式 */
.file-area {
    min-height: 400px;
    position: relative;
}

.file-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

/* 加载和空状态 */
.loading-state, .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #666;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #333;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .nav-container {
        padding: 0 15px;
        gap: 15px;
    }

    .nav-brand {
        min-width: 150px;
    }

    .brand-text {
        display: none;
    }

    .search-container {
        max-width: 400px;
        min-width: 250px;
    }
}

@media (max-width: 768px) {
    .nav-container {
        height: auto;
        padding: 10px;
        flex-wrap: wrap;
        gap: 10px;
    }

    .nav-brand {
        order: 1;
        min-width: auto;
    }

    .user-menu {
        order: 2;
        min-width: auto;
    }

    .search-container {
        order: 3;
        width: 100%;
        max-width: none;
        min-width: auto;
        margin: 0;
    }

    .search-engine-toggle {
        margin-bottom: 8px;
    }

    .main-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
        padding: 15px;
    }

    .toolbar {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .toolbar-left, .toolbar-right {
        justify-content: center;
    }

    .file-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 8px;
    }

    .navbar {
        position: relative;
    }

    .search-container {
        margin-top: 10px;
    }

    .search-toggle {
        font-size: 10px;
        padding: 2px 8px;
    }

    .file-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }

    .sidebar {
        padding: 10px;
    }

    .content-area {
        padding: 15px;
    }
}
