# -*- coding: utf-8 -*-
"""
Application Startup Script
应用启动脚本
"""
import os
import sys
import uvicorn
from loguru import logger

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config import get_settings

settings = get_settings()


def main():
    """主函数"""
    logger.info("Starting Enterprise File Sharing System...")
    logger.info(f"Service Address: http://{settings.HOST}:{settings.PORT}")
    logger.info(f"Debug Mode: {'ON' if settings.DEBUG else 'OFF'}")
    logger.info(f"API Docs: http://{settings.HOST}:{settings.PORT}/docs")
    
    # 启动服务
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True,
        use_colors=True
    )


if __name__ == "__main__":
    main()
