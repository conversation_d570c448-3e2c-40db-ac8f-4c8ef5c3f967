<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🔧 登录功能测试</h1>
    
    <div class="test-section">
        <h3>管理员登录测试</h3>
        <button onclick="testAdminLogin()">测试管理员登录 (admin/admin123)</button>
        <div id="admin-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>用户登录测试</h3>
        <button onclick="testUserLogin()">测试用户登录 (test/123456)</button>
        <div id="user-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>错误登录测试</h3>
        <button onclick="testWrongLogin()">测试错误密码</button>
        <div id="wrong-result" class="result"></div>
    </div>

    <script>
        async function testAdminLogin() {
            const resultEl = document.getElementById('admin-result');
            resultEl.textContent = '正在测试管理员登录...';
            resultEl.className = 'result';
            
            try {
                const response = await fetch('http://localhost:3000/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultEl.className = 'result success';
                    resultEl.textContent = `✅ 管理员登录成功！\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultEl.className = 'result error';
                    resultEl.textContent = `❌ 管理员登录失败 (${response.status})\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultEl.className = 'result error';
                resultEl.textContent = `❌ 网络错误: ${error.message}`;
            }
        }
        
        async function testUserLogin() {
            const resultEl = document.getElementById('user-result');
            resultEl.textContent = '正在测试用户登录...';
            resultEl.className = 'result';
            
            try {
                const response = await fetch('http://localhost:3000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'test',
                        password: '123456'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultEl.className = 'result success';
                    resultEl.textContent = `✅ 用户登录成功！\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultEl.className = 'result error';
                    resultEl.textContent = `❌ 用户登录失败 (${response.status})\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultEl.className = 'result error';
                resultEl.textContent = `❌ 网络错误: ${error.message}`;
            }
        }
        
        async function testWrongLogin() {
            const resultEl = document.getElementById('wrong-result');
            resultEl.textContent = '正在测试错误密码...';
            resultEl.className = 'result';
            
            try {
                const response = await fetch('http://localhost:3000/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'wrongpassword'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultEl.className = 'result error';
                    resultEl.textContent = `❌ 应该失败但成功了！\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultEl.className = 'result success';
                    resultEl.textContent = `✅ 正确拒绝了错误密码 (${response.status})\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultEl.className = 'result error';
                resultEl.textContent = `❌ 网络错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
