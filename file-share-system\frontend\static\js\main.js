// 主应用程序

class FileShareApp {
    constructor() {
        this.currentFolder = null;
        this.selectedFiles = new Set();
        this.currentView = 'grid';
        this.currentSort = 'name';
        this.searchEngine = 'filename';
        
        this.init();
    }

    async init() {
        console.log('🚀 初始化文件共享系统...');
        
        // 检查用户登录状态
        await this.checkAuthStatus();
        
        // 初始化事件监听器
        this.initEventListeners();
        
        // 初始化拖拽上传
        this.initDragAndDrop();
        
        // 加载初始数据
        await this.loadInitialData();
        
        // 启动定时任务
        this.startPeriodicTasks();
        
        console.log('✅ 系统初始化完成');
    }

    // 检查认证状态
    async checkAuthStatus() {
        const accessToken = localStorage.getItem('access_token');
        const userRole = localStorage.getItem('user_role');

        // 检查是否有有效的用户令牌
        if (!accessToken || userRole !== 'user') {
            console.log('👤 用户未登录，重定向到登录页面');
            window.location.href = 'user-login.html';
            return;
        }

        try {
            const user = await api.getCurrentUser();
            this.updateUserInfo(user);
            console.log('👤 用户已登录:', user.username);
        } catch (error) {
            console.log('👤 令牌无效，重定向到登录页面');
            // 清除无效令牌
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user_role');
            window.location.href = 'user-login.html';
        }
    }

    // 更新用户信息显示
    updateUserInfo(user) {
        const userInfo = document.getElementById('user-info');
        const userName = userInfo.querySelector('.user-name');

        userName.textContent = user.username || user.full_name || '用户';

        // 存储用户信息
        this.currentUser = user;
    }

    // 显示个人资料模态框
    showProfileModal() {
        if (!this.currentUser) {
            this.showNotification('用户信息加载中...', 'info');
            return;
        }

        const modal = createModal('个人资料', `
            <div class="profile-info">
                <div class="form-group">
                    <label class="form-label">用户名</label>
                    <input type="text" class="form-input" value="${this.currentUser.username}" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">邮箱</label>
                    <input type="email" class="form-input" value="${this.currentUser.email || ''}" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">真实姓名</label>
                    <input type="text" class="form-input" value="${this.currentUser.full_name || ''}" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">注册时间</label>
                    <input type="text" class="form-input" value="${formatDate(this.currentUser.created_at)}" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">用户角色</label>
                    <input type="text" class="form-input" value="${this.currentUser.role === 'admin' ? '管理员' : '普通用户'}" readonly>
                </div>
            </div>
        `, [
            {
                text: '关闭',
                class: 'btn-primary',
                onclick: () => closeModal()
            }
        ]);

        showModal(modal);
    }

    // 退出登录
    async logout() {
        try {
            await api.logout();
        } catch (error) {
            console.error('退出登录请求失败:', error);
        }

        // 清除本地存储
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user_role');

        this.showNotification('已退出登录', 'success');

        // 跳转到登录页面
        setTimeout(() => {
            window.location.href = 'user-login.html';
        }, 1000);
    }

    // 切换用户下拉菜单
    toggleUserDropdown() {
        const dropdown = document.getElementById('user-dropdown');
        const isVisible = dropdown.style.display === 'block';

        if (isVisible) {
            dropdown.style.display = 'none';
        } else {
            dropdown.style.display = 'block';
        }
    }

    // 初始化事件监听器
    initEventListeners() {
        // 搜索引擎切换
        document.getElementById('filename-search').addEventListener('click', () => {
            this.switchSearchEngine('filename');
        });
        
        document.getElementById('image-search').addEventListener('click', () => {
            this.switchSearchEngine('image');
        });

        // 搜索功能
        const searchInput = document.getElementById('search-input');
        const searchBtn = document.getElementById('search-btn');
        
        searchInput.addEventListener('input', debounce(() => {
            this.performSearch(searchInput.value);
        }, 300));
        
        searchBtn.addEventListener('click', () => {
            this.performSearch(searchInput.value);
        });

        // 用户菜单
        document.getElementById('user-info').addEventListener('click', () => {
            this.toggleUserDropdown();
        });

        document.getElementById('profile-btn').addEventListener('click', () => {
            this.showProfileModal();
        });

        document.getElementById('logout-btn').addEventListener('click', () => {
            this.logout();
        });

        // 工具栏按钮
        document.getElementById('upload-btn').addEventListener('click', () => {
            this.showUploadModal();
        });

        document.getElementById('new-folder-btn').addEventListener('click', () => {
            this.showNewFolderModal();
        });

        // 视图切换
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchView(e.target.dataset.view);
            });
        });

        // 排序
        document.getElementById('sort-select').addEventListener('change', (e) => {
            this.changeSortOrder(e.target.value);
        });

        // 全局点击事件（关闭下拉菜单等）
        document.addEventListener('click', (e) => {
            this.handleGlobalClick(e);
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    // 初始化拖拽上传
    initDragAndDrop() {
        const fileArea = document.querySelector('.file-area');
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            fileArea.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            fileArea.addEventListener(eventName, () => {
                fileArea.classList.add('dragover');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            fileArea.addEventListener(eventName, () => {
                fileArea.classList.remove('dragover');
            }, false);
        });

        fileArea.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            this.handleFileUpload(files);
        }, false);
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    // 加载初始数据
    async loadInitialData() {
        try {
            // 加载文件列表
            await this.loadFiles();
            
            // 加载文件夹树
            await this.loadFolderTree();
            
            // 加载统计信息
            await this.loadStats();
            
        } catch (error) {
            console.error('加载初始数据失败:', error);
            this.showNotification('加载数据失败', 'error');
        }
    }

    // 加载文件列表
    async loadFiles(folderId = null) {
        try {
            this.showLoading(true);
            
            const response = await api.getFiles(folderId);
            this.renderFiles(response.files || []);
            this.updateBreadcrumb(response.path || []);
            
        } catch (error) {
            console.error('加载文件失败:', error);
            this.showNotification('加载文件失败', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // 渲染文件列表
    renderFiles(files) {
        const fileGrid = document.getElementById('file-grid');
        const emptyState = document.getElementById('empty-state');
        
        if (files.length === 0) {
            fileGrid.innerHTML = '';
            emptyState.style.display = 'flex';
            return;
        }
        
        emptyState.style.display = 'none';
        
        fileGrid.innerHTML = files.map(file => this.createFileItem(file)).join('');
        
        // 添加文件项事件监听器
        this.attachFileItemListeners();
    }

    // 创建文件项HTML
    createFileItem(file) {
        const icon = getFileIcon(file.filename, file.is_folder);
        const typeClass = file.is_folder ? 'folder' : getFileTypeClass(file.filename);
        const size = file.is_folder ? '' : formatFileSize(file.file_size);
        const date = formatDate(file.created_at);
        
        return `
            <div class="file-item" data-file-id="${file.id}" data-is-folder="${file.is_folder}">
                <div class="file-menu">
                    <button class="file-menu-btn" onclick="app.showFileMenu(event, ${file.id})">⋮</button>
                </div>
                
                ${file.thumbnail_path ? 
                    `<img src="${file.thumbnail_path}" alt="${file.filename}" class="file-thumbnail">` :
                    `<span class="file-icon ${typeClass}">${icon}</span>`
                }
                
                <div class="file-name" title="${file.filename}">${file.filename}</div>
                <div class="file-info">
                    <span class="file-size">${size}</span>
                    <span class="file-date">${date}</span>
                </div>
                
                ${file.is_sensitive ? '<span class="tag sensitive">敏感</span>' : ''}
                ${file.is_encrypted ? '<span class="tag encrypted">加密</span>' : ''}
            </div>
        `;
    }

    // 附加文件项事件监听器
    attachFileItemListeners() {
        document.querySelectorAll('.file-item').forEach(item => {
            item.addEventListener('click', (e) => {
                this.handleFileItemClick(e, item);
            });
            
            item.addEventListener('dblclick', (e) => {
                this.handleFileItemDoubleClick(e, item);
            });
            
            item.addEventListener('contextmenu', (e) => {
                this.showContextMenu(e, item);
            });
        });
    }

    // 处理文件项点击
    handleFileItemClick(e, item) {
        if (e.ctrlKey || e.metaKey) {
            // 多选
            this.toggleFileSelection(item);
        } else {
            // 单选
            this.clearFileSelection();
            this.selectFile(item);
        }
    }

    // 处理文件项双击
    handleFileItemDoubleClick(e, item) {
        const fileId = item.dataset.fileId;
        const isFolder = item.dataset.isFolder === 'true';
        
        if (isFolder) {
            this.openFolder(fileId);
        } else {
            this.openFile(fileId);
        }
    }

    // 显示加载状态
    showLoading(show) {
        const loadingState = document.getElementById('loading-state');
        const fileGrid = document.getElementById('file-grid');
        
        if (show) {
            loadingState.style.display = 'flex';
            fileGrid.style.display = 'none';
        } else {
            loadingState.style.display = 'none';
            fileGrid.style.display = 'grid';
        }
    }

    // 显示通知
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-header">
                <span class="notification-title">${type === 'error' ? '错误' : type === 'success' ? '成功' : '提示'}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
            <div class="notification-message">${message}</div>
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => notification.classList.add('show'), 100);

        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, duration);
        }
    }

    // 显示登录模态框
    showLoginModal() {
        showLoginModal();
    }

    // 显示上传模态框
    showUploadModal() {
        const modal = createModal('上传文件', `
            <div class="upload-area" id="upload-area">
                <div class="upload-icon">📤</div>
                <div class="upload-text">拖拽文件到这里或点击选择</div>
                <div class="upload-hint">支持多文件上传，单个文件最大100MB</div>
                <input type="file" id="file-input" multiple style="display: none;">
            </div>
            <div id="upload-progress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div id="upload-status">准备上传...</div>
            </div>
        `, [
            {
                text: '取消',
                class: 'btn-secondary',
                onclick: () => closeModal()
            }
        ]);

        showModal(modal);

        // 初始化上传区域事件
        this.initUploadAreaEvents();
    }

    // 显示新建文件夹模态框
    showNewFolderModal() {
        const modal = createModal('新建文件夹', `
            <form id="new-folder-form">
                <div class="form-group">
                    <label class="form-label">文件夹名称</label>
                    <input type="text" id="folder-name" class="form-input" placeholder="请输入文件夹名称" required>
                </div>
            </form>
        `, [
            {
                text: '取消',
                class: 'btn-secondary',
                onclick: () => closeModal()
            },
            {
                text: '创建',
                class: 'btn-primary',
                onclick: () => this.handleCreateFolder()
            }
        ]);

        showModal(modal);

        // 焦点到输入框
        setTimeout(() => {
            document.getElementById('folder-name').focus();
        }, 100);

        // 回车提交
        document.getElementById('new-folder-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleCreateFolder();
        });
    }

    // 启动定时任务
    startPeriodicTasks() {
        // 每30秒更新统计信息
        setInterval(() => {
            this.loadStats();
        }, 30000);
        
        // 每5分钟检查系统健康状态
        setInterval(() => {
            this.checkSystemHealth();
        }, 300000);
    }

    // 加载统计信息
    async loadStats() {
        try {
            const stats = await api.getSystemStats();
            this.updateStatsDisplay(stats);
        } catch (error) {
            console.error('加载统计信息失败:', error);
        }
    }

    // 更新统计信息显示
    updateStatsDisplay(stats) {
        document.getElementById('total-files').textContent = formatNumber(stats.total_files || 0);
        document.getElementById('online-users').textContent = formatNumber(stats.online_users || 0);
        document.getElementById('storage-used').textContent = formatFileSize(stats.storage_used || 0);
    }

    // 检查系统健康状态
    async checkSystemHealth() {
        try {
            const health = await api.getSystemHealth();
            if (health.status !== 'healthy') {
                this.showNotification('系统服务异常，请联系管理员', 'warning');
            }
        } catch (error) {
            console.error('健康检查失败:', error);
        }
    }
}

// 创建全局应用实例
const app = new FileShareApp();

// 导出应用实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { FileShareApp, app };
}
