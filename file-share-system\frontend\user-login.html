<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 企业级文件共享系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 450px;
            margin: 20px;
        }

        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .user-icon {
            font-size: 3em;
            margin-bottom: 10px;
            display: block;
        }

        .login-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .login-subtitle {
            opacity: 0.9;
            font-size: 0.9em;
        }

        .login-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }

        .tab-btn {
            flex: 1;
            padding: 15px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            color: #666;
        }

        .tab-btn.active {
            background: white;
            color: #667eea;
            border-bottom: 2px solid #667eea;
        }

        .login-form {
            padding: 40px 30px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s;
            background: #f8f9fa;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 25px;
        }

        .form-checkbox input {
            width: 18px;
            height: 18px;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .register-btn {
            background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
        }

        .register-btn:hover {
            box-shadow: 0 10px 20px rgba(72, 219, 251, 0.3);
        }

        .login-links {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }

        .login-link {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s;
            margin: 0 10px;
        }

        .login-link:hover {
            color: #5a6fd8;
            text-decoration: underline;
        }

        .admin-link {
            color: #ff6b6b;
        }

        .admin-link:hover {
            color: #ee5a24;
        }

        .error-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }

        .success-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 10px;
            }
            
            .login-header {
                padding: 25px 20px;
            }
            
            .login-form {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <span class="user-icon">👥</span>
            <h1 class="login-title">用户登录</h1>
            <p class="login-subtitle">企业级文件共享系统</p>
        </div>

        <div class="login-tabs">
            <button class="tab-btn active" onclick="switchTab('login')">登录</button>
            <button class="tab-btn" onclick="switchTab('register')">注册</button>
        </div>

        <div class="login-form">
            <div id="error-message" class="error-message"></div>
            <div id="success-message" class="success-message"></div>

            <!-- 登录表单 -->
            <div id="login-tab" class="tab-content active">
                <form id="user-login-form">
                    <div class="form-group">
                        <label class="form-label">用户名</label>
                        <input type="text" id="login-username" class="form-input" placeholder="请输入用户名" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">密码</label>
                        <input type="password" id="login-password" class="form-input" placeholder="请输入密码" required>
                    </div>

                    <div class="form-checkbox">
                        <input type="checkbox" id="login-remember">
                        <label for="login-remember">记住登录状态</label>
                    </div>

                    <button type="submit" id="user-login-btn" class="login-btn">
                        登录系统
                    </button>
                </form>
            </div>

            <!-- 注册表单 -->
            <div id="register-tab" class="tab-content">
                <form id="user-register-form">
                    <div class="form-group">
                        <label class="form-label">用户名</label>
                        <input type="text" id="register-username" class="form-input" placeholder="请输入用户名" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">邮箱</label>
                        <input type="email" id="register-email" class="form-input" placeholder="请输入邮箱地址" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">密码</label>
                        <input type="password" id="register-password" class="form-input" placeholder="请输入密码（至少6位）" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">确认密码</label>
                        <input type="password" id="register-confirm" class="form-input" placeholder="请再次输入密码" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">真实姓名</label>
                        <input type="text" id="register-fullname" class="form-input" placeholder="请输入真实姓名">
                    </div>

                    <button type="submit" id="user-register-btn" class="login-btn register-btn">
                        注册账户
                    </button>
                </form>
            </div>

            <div class="login-links">
                <a href="admin-login.html" class="login-link admin-link">管理员登录</a>
                <span>|</span>
                <a href="#" class="login-link">忘记密码？</a>
            </div>
        </div>
    </div>

    <script>
        // 切换标签页
        function switchTab(tabName) {
            // 更新标签按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 更新内容显示
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tabName + '-tab').classList.add('active');
            
            hideMessages();
        }

        // 用户登录处理
        document.getElementById('user-login-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('login-username').value.trim();
            const password = document.getElementById('login-password').value;
            const remember = document.getElementById('login-remember').checked;
            
            if (!username || !password) {
                showError('请输入用户名和密码');
                return;
            }
            
            const loginBtn = document.getElementById('user-login-btn');
            const originalText = loginBtn.textContent;
            
            try {
                loginBtn.textContent = '登录中...';
                loginBtn.disabled = true;
                hideMessages();
                
                const response = await fetch('http://localhost:8000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username,
                        password,
                        remember_me: remember
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    localStorage.setItem('access_token', data.access_token);
                    localStorage.setItem('user_role', 'user');
                    
                    if (data.refresh_token) {
                        localStorage.setItem('refresh_token', data.refresh_token);
                    }
                    
                    showSuccess('登录成功，正在跳转...');
                    
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1000);
                    
                } else {
                    showError(data.message || '登录失败，请检查用户名和密码');
                }
                
            } catch (error) {
                console.error('登录错误:', error);
                showError('网络连接失败，请检查后端服务是否启动');
            } finally {
                loginBtn.textContent = originalText;
                loginBtn.disabled = false;
            }
        });

        // 用户注册处理
        document.getElementById('user-register-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('register-username').value.trim();
            const email = document.getElementById('register-email').value.trim();
            const password = document.getElementById('register-password').value;
            const confirmPassword = document.getElementById('register-confirm').value;
            const fullName = document.getElementById('register-fullname').value.trim();
            
            if (!username || !email || !password) {
                showError('请填写必填字段');
                return;
            }
            
            if (password !== confirmPassword) {
                showError('两次输入的密码不一致');
                return;
            }
            
            if (password.length < 6) {
                showError('密码长度至少6位');
                return;
            }
            
            const registerBtn = document.getElementById('user-register-btn');
            const originalText = registerBtn.textContent;
            
            try {
                registerBtn.textContent = '注册中...';
                registerBtn.disabled = true;
                hideMessages();
                
                const response = await fetch('http://localhost:8000/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username,
                        email,
                        password,
                        full_name: fullName
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showSuccess('注册成功！请切换到登录标签页进行登录。');
                    
                    // 清空表单
                    document.getElementById('user-register-form').reset();
                    
                    // 3秒后自动切换到登录标签
                    setTimeout(() => {
                        switchTab('login');
                        document.getElementById('login-username').value = username;
                    }, 2000);
                    
                } else {
                    showError(data.message || '注册失败');
                }
                
            } catch (error) {
                console.error('注册错误:', error);
                showError('网络连接失败，请检查后端服务是否启动');
            } finally {
                registerBtn.textContent = originalText;
                registerBtn.disabled = false;
            }
        });
        
        // 显示错误消息
        function showError(message) {
            const errorEl = document.getElementById('error-message');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
            
            setTimeout(() => {
                errorEl.style.display = 'none';
            }, 5000);
        }
        
        // 显示成功消息
        function showSuccess(message) {
            const successEl = document.getElementById('success-message');
            successEl.textContent = message;
            successEl.style.display = 'block';
        }
        
        // 隐藏所有消息
        function hideMessages() {
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-message').style.display = 'none';
        }
        
        // 检查是否已经登录
        window.addEventListener('load', () => {
            const accessToken = localStorage.getItem('access_token');
            const userRole = localStorage.getItem('user_role');
            
            if (accessToken && userRole === 'user') {
                window.location.href = 'index.html';
            }
        });
    </script>
</body>
</html>
