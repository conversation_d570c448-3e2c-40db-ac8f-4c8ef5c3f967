# 企业级文件共享系统环境配置文件

# 应用配置
APP_NAME=企业级文件共享系统
APP_VERSION=1.0.0
DEBUG=true

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=mysql+pymysql://root:123456@localhost:3306/file_share_system
DATABASE_ECHO=false

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# JWT配置
SECRET_KEY=your-secret-key-change-this-in-production-environment
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# 文件存储配置
UPLOAD_DIR=storage/uploads
THUMBNAIL_DIR=storage/thumbnails
TEMP_DIR=storage/temp
MAX_FILE_SIZE=104857600
ALLOWED_EXTENSIONS=["jpg","jpeg","png","gif","bmp","tiff","tif","psd","ai","eps","svg","pdf","doc","docx","xls","xlsx","ppt","pptx","txt","zip","rar"]

# 缩略图配置
THUMBNAIL_SIZE=(200,200)
THUMBNAIL_QUALITY=85

# 安全配置
CORS_ORIGINS=["*"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["*"]
CORS_ALLOW_HEADERS=["*"]

# 权限配置
DEFAULT_USER_ROLE=user
ADMIN_ROLE=admin
READONLY_ROLE=readonly

# 登录安全配置
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=30

# 下载配置
MAX_DOWNLOAD_FILES=10
MAX_DOWNLOAD_SIZE_MB=500
DOWNLOAD_SPEED_LIMIT_KB=1024

# 加密配置
ENCRYPTION_ENABLED=true
ENCRYPTION_TRIGGER_COUNT=3
PASSWORD_REQUEST_LIMIT=5

# 搜索配置
SEARCH_RESULTS_PER_PAGE=20
MAX_SEARCH_RESULTS=1000

# 图像识别配置
IMAGE_FEATURE_EXTRACTION=true
SIMILARITY_THRESHOLD=0.8

# 监控配置
ENABLE_MONITORING=true
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# 网络访问控制
INTERNAL_NETWORKS=["***********/16","10.0.0.0/8","**********/12"]
EXTERNAL_ACCESS_ENABLED=false
IP_WHITELIST=[]

# 限流配置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW_SECONDS=60

# 备份配置
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30
