"""
认证路由
"""
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import HTTPBearer
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
from passlib.context import CryptContext
from jose import JWTError, jwt
from loguru import logger

from app.config import get_settings
from app.database import get_db, cache_manager, CacheKeys
from app.models.user import User, UserRole
from app.models.activity_log import ActivityLog, ActionType, TargetType, LogLevel

settings = get_settings()
router = APIRouter()
security = HTTPBearer()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


# Pydantic模型
class UserLogin(BaseModel):
    username: str
    password: str
    remember_me: bool = False


class UserRegister(BaseModel):
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None
    phone: Optional[str] = None
    department: Optional[str] = None


class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str
    expires_in: int
    user_info: dict


class UserResponse(BaseModel):
    id: int
    username: str
    email: Optional[str]
    full_name: Optional[str]
    role: str
    is_active: bool
    created_at: str


# 密码工具函数
def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """生成密码哈希"""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def create_refresh_token(data: dict):
    """创建刷新令牌"""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


async def authenticate_user(db: Session, username: str, password: str, client_ip: str) -> Optional[User]:
    """认证用户"""
    # 检查登录尝试次数
    attempts_key = CacheKeys.user_login_attempts(username)
    attempts = await cache_manager.get(attempts_key)
    attempts = int(attempts) if attempts else 0
    
    if attempts >= settings.MAX_LOGIN_ATTEMPTS:
        raise HTTPException(
            status_code=status.HTTP_423_LOCKED,
            detail=f"登录尝试次数过多，请{settings.LOCKOUT_DURATION_MINUTES}分钟后再试"
        )
    
    # 查找用户
    user = db.query(User).filter(
        (User.username == username) | (User.email == username)
    ).first()
    
    if not user or not verify_password(password, user.password_hash):
        # 增加失败尝试次数
        await cache_manager.increment(attempts_key)
        await cache_manager.expire(attempts_key, settings.LOCKOUT_DURATION_MINUTES * 60)
        return None
    
    # 检查用户状态
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="账户已被禁用"
        )
    
    if user.is_locked():
        raise HTTPException(
            status_code=status.HTTP_423_LOCKED,
            detail="账户已被锁定"
        )
    
    # 清除登录尝试次数
    await cache_manager.delete(attempts_key)
    
    # 更新最后登录信息
    user.last_login = datetime.now()
    user.last_login_ip = client_ip
    user.login_attempts = 0
    db.commit()
    
    return user


@router.post("/login", response_model=Token)
async def login(
    user_data: UserLogin,
    request: Request,
    db: Session = Depends(get_db)
):
    """用户登录"""
    client_ip = request.client.host if request.client else "unknown"
    
    try:
        # 认证用户
        user = await authenticate_user(db, user_data.username, user_data.password, client_ip)
        if not user:
            # 记录登录失败日志
            activity_log = ActivityLog(
                action_type=ActionType.LOGIN,
                target_type=TargetType.USER,
                username=user_data.username,
                ip_address=client_ip,
                user_agent=request.headers.get("User-Agent", ""),
                result="failed",
                error_message="用户名或密码错误",
                log_level=LogLevel.WARNING
            )
            db.add(activity_log)
            db.commit()
            
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 创建令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        if user_data.remember_me:
            access_token_expires = timedelta(days=7)  # 记住登录延长到7天
        
        access_token = create_access_token(
            data={"sub": str(user.id)},
            expires_delta=access_token_expires
        )
        refresh_token = create_refresh_token(data={"sub": str(user.id)})
        
        # 存储会话信息到Redis
        session_key = CacheKeys.user_session(user.id)
        await cache_manager.set(
            session_key, 
            access_token, 
            expire=int(access_token_expires.total_seconds())
        )
        
        # 记录登录成功日志
        activity_log = ActivityLog(
            action_type=ActionType.LOGIN,
            target_type=TargetType.USER,
            user_id=user.id,
            username=user.username,
            ip_address=client_ip,
            user_agent=request.headers.get("User-Agent", ""),
            result="success",
            log_level=LogLevel.INFO
        )
        db.add(activity_log)
        db.commit()
        
        logger.info(f"用户登录成功: {user.username} - IP: {client_ip}")
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": int(access_token_expires.total_seconds()),
            "user_info": {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "full_name": user.full_name,
                "role": user.role.value,
                "is_active": user.is_active
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录过程中发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录服务暂时不可用"
        )


@router.post("/register", response_model=UserResponse)
async def register(
    user_data: UserRegister,
    request: Request,
    db: Session = Depends(get_db)
):
    """用户注册"""
    # 检查是否允许注册
    # 这里应该从系统配置中读取，简化处理
    registration_enabled = True
    if not registration_enabled:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户注册已关闭"
        )
    
    # 检查用户名是否已存在
    existing_user = db.query(User).filter(
        (User.username == user_data.username) | (User.email == user_data.email)
    ).first()
    
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名或邮箱已存在"
        )
    
    # 创建新用户
    hashed_password = get_password_hash(user_data.password)
    new_user = User(
        username=user_data.username,
        email=user_data.email,
        password_hash=hashed_password,
        full_name=user_data.full_name,
        phone=user_data.phone,
        department=user_data.department,
        role=UserRole.USER,  # 默认为普通用户
        is_active=True
    )
    
    try:
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        # 记录注册日志
        client_ip = request.client.host if request.client else "unknown"
        activity_log = ActivityLog(
            action_type=ActionType.USER_CREATE,
            target_type=TargetType.USER,
            target_id=new_user.id,
            target_name=new_user.username,
            username=new_user.username,
            ip_address=client_ip,
            user_agent=request.headers.get("User-Agent", ""),
            result="success",
            log_level=LogLevel.INFO
        )
        db.add(activity_log)
        db.commit()
        
        logger.info(f"新用户注册成功: {new_user.username}")
        
        return UserResponse(
            id=new_user.id,
            username=new_user.username,
            email=new_user.email,
            full_name=new_user.full_name,
            role=new_user.role.value,
            is_active=new_user.is_active,
            created_at=new_user.created_at.isoformat()
        )
        
    except Exception as e:
        db.rollback()
        logger.error(f"用户注册失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册失败，请稍后再试"
        )


@router.post("/logout")
async def logout(
    request: Request,
    db: Session = Depends(get_db)
):
    """用户登出"""
    # 获取用户信息
    user_id = getattr(request.state, "user_id", None)
    username = getattr(request.state, "user", None)
    username = username.username if username else "unknown"
    
    if user_id:
        # 清除会话信息
        session_key = CacheKeys.user_session(user_id)
        await cache_manager.delete(session_key)
        
        # 记录登出日志
        client_ip = request.client.host if request.client else "unknown"
        activity_log = ActivityLog(
            action_type=ActionType.LOGOUT,
            target_type=TargetType.USER,
            user_id=user_id,
            username=username,
            ip_address=client_ip,
            user_agent=request.headers.get("User-Agent", ""),
            result="success",
            log_level=LogLevel.INFO
        )
        db.add(activity_log)
        db.commit()
        
        logger.info(f"用户登出: {username}")
    
    return {"message": "登出成功"}


@router.post("/refresh", response_model=Token)
async def refresh_token(
    refresh_token: str,
    db: Session = Depends(get_db)
):
    """刷新令牌"""
    try:
        # 验证刷新令牌
        payload = jwt.decode(refresh_token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        user_id: int = int(payload.get("sub"))
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的刷新令牌"
        )
    
    # 获取用户信息
    user = db.query(User).filter(User.id == user_id).first()
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在或已被禁用"
        )
    
    # 创建新的访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)},
        expires_delta=access_token_expires
    )
    new_refresh_token = create_refresh_token(data={"sub": str(user.id)})
    
    # 更新会话信息
    session_key = CacheKeys.user_session(user.id)
    await cache_manager.set(
        session_key, 
        access_token, 
        expire=int(access_token_expires.total_seconds())
    )
    
    return {
        "access_token": access_token,
        "refresh_token": new_refresh_token,
        "token_type": "bearer",
        "expires_in": int(access_token_expires.total_seconds()),
        "user_info": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "role": user.role.value,
            "is_active": user.is_active
        }
    }


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    request: Request,
    db: Session = Depends(get_db)
):
    """获取当前用户信息"""
    user_id = getattr(request.state, "user_id", None)
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未授权访问"
        )
    
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return UserResponse(
        id=user.id,
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        role=user.role.value,
        is_active=user.is_active,
        created_at=user.created_at.isoformat()
    )
