// 搜索功能

// 切换搜索引擎
function switchSearchEngine(engine) {
    app.searchEngine = engine;
    
    // 更新按钮状态
    document.querySelectorAll('.search-toggle').forEach(btn => {
        btn.classList.remove('active');
    });
    
    document.getElementById(`${engine}-search`).classList.add('active');
    
    // 更新搜索输入框占位符
    const searchInput = document.getElementById('search-input');
    if (engine === 'filename') {
        searchInput.placeholder = '搜索文件名...';
        searchInput.type = 'text';
    } else if (engine === 'image') {
        searchInput.placeholder = '上传图片进行相似图片搜索...';
        searchInput.type = 'file';
        searchInput.accept = 'image/*';
    }
    
    // 清空当前搜索
    searchInput.value = '';
    hideSearchResults();
}

// 执行搜索
async function performSearch(query) {
    if (!query || query.trim().length < 2) {
        hideSearchResults();
        return;
    }
    
    try {
        let results;
        
        if (app.searchEngine === 'filename') {
            results = await api.searchFiles(query.trim(), 'filename');
        } else if (app.searchEngine === 'image') {
            // 图片搜索需要特殊处理
            const fileInput = document.getElementById('search-input');
            if (fileInput.files && fileInput.files[0]) {
                results = await api.searchByImage(fileInput.files[0]);
            } else {
                return;
            }
        }
        
        displaySearchResults(results.files || []);
        
    } catch (error) {
        console.error('搜索失败:', error);
        app.showNotification('搜索失败: ' + error.message, 'error');
        hideSearchResults();
    }
}

// 显示搜索结果
function displaySearchResults(results) {
    let searchResultsContainer = document.querySelector('.search-results');
    
    if (!searchResultsContainer) {
        searchResultsContainer = document.createElement('div');
        searchResultsContainer.className = 'search-results';
        
        const searchContainer = document.querySelector('.search-container');
        searchContainer.appendChild(searchResultsContainer);
    }
    
    if (results.length === 0) {
        searchResultsContainer.innerHTML = `
            <div class="search-result-item">
                <div class="search-result-info">
                    <div class="search-result-name">未找到匹配的文件</div>
                </div>
            </div>
        `;
    } else {
        searchResultsContainer.innerHTML = results.map(file => `
            <div class="search-result-item" onclick="selectSearchResult(${file.id}, '${file.filename}', ${file.is_folder})">
                <span class="search-result-icon">${getFileIcon(file.filename, file.is_folder)}</span>
                <div class="search-result-info">
                    <div class="search-result-name">${file.filename}</div>
                    <div class="search-result-path">${file.folder_path || '根目录'}</div>
                </div>
            </div>
        `).join('');
    }
    
    searchResultsContainer.style.display = 'block';
}

// 隐藏搜索结果
function hideSearchResults() {
    const searchResults = document.querySelector('.search-results');
    if (searchResults) {
        searchResults.style.display = 'none';
    }
}

// 选择搜索结果
async function selectSearchResult(fileId, filename, isFolder) {
    hideSearchResults();
    
    if (isFolder) {
        // 打开文件夹
        await openFolder(fileId);
    } else {
        // 高亮显示文件
        await app.loadFiles(null); // 先加载根目录
        
        // 查找并高亮文件
        setTimeout(() => {
            const fileItem = document.querySelector(`[data-file-id="${fileId}"]`);
            if (fileItem) {
                fileItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
                fileItem.classList.add('highlight');
                
                // 3秒后移除高亮
                setTimeout(() => {
                    fileItem.classList.remove('highlight');
                }, 3000);
            }
        }, 500);
    }
    
    // 清空搜索框
    document.getElementById('search-input').value = '';
}

// 高级搜索功能
function showAdvancedSearch() {
    const modal = createModal('高级搜索', `
        <div class="advanced-search-form">
            <div class="form-group">
                <label class="form-label">文件名</label>
                <input type="text" id="adv-filename" class="form-input" placeholder="支持通配符 * 和 ?">
            </div>
            
            <div class="form-group">
                <label class="form-label">文件类型</label>
                <select id="adv-filetype" class="form-input">
                    <option value="">所有类型</option>
                    <option value="image">图片</option>
                    <option value="document">文档</option>
                    <option value="video">视频</option>
                    <option value="audio">音频</option>
                    <option value="archive">压缩包</option>
                    <option value="code">代码</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">文件大小</label>
                <div style="display: flex; gap: 10px; align-items: center;">
                    <input type="number" id="adv-size-min" class="form-input" placeholder="最小(MB)" style="flex: 1;">
                    <span>-</span>
                    <input type="number" id="adv-size-max" class="form-input" placeholder="最大(MB)" style="flex: 1;">
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">修改时间</label>
                <div style="display: flex; gap: 10px;">
                    <input type="date" id="adv-date-from" class="form-input" style="flex: 1;">
                    <input type="date" id="adv-date-to" class="form-input" style="flex: 1;">
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">上传者</label>
                <input type="text" id="adv-uploader" class="form-input" placeholder="用户名">
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="adv-include-content"> 搜索文件内容
                </label>
            </div>
        </div>
    `, [
        {
            text: '重置',
            class: 'btn-secondary',
            onclick: () => resetAdvancedSearch()
        },
        {
            text: '搜索',
            class: 'btn-primary',
            onclick: () => performAdvancedSearch()
        }
    ]);
    
    showModal(modal);
}

// 重置高级搜索
function resetAdvancedSearch() {
    document.getElementById('adv-filename').value = '';
    document.getElementById('adv-filetype').value = '';
    document.getElementById('adv-size-min').value = '';
    document.getElementById('adv-size-max').value = '';
    document.getElementById('adv-date-from').value = '';
    document.getElementById('adv-date-to').value = '';
    document.getElementById('adv-uploader').value = '';
    document.getElementById('adv-include-content').checked = false;
}

// 执行高级搜索
async function performAdvancedSearch() {
    const searchParams = {
        filename: document.getElementById('adv-filename').value.trim(),
        filetype: document.getElementById('adv-filetype').value,
        size_min: document.getElementById('adv-size-min').value,
        size_max: document.getElementById('adv-size-max').value,
        date_from: document.getElementById('adv-date-from').value,
        date_to: document.getElementById('adv-date-to').value,
        uploader: document.getElementById('adv-uploader').value.trim(),
        include_content: document.getElementById('adv-include-content').checked
    };
    
    // 移除空值
    Object.keys(searchParams).forEach(key => {
        if (!searchParams[key]) {
            delete searchParams[key];
        }
    });
    
    if (Object.keys(searchParams).length === 0) {
        app.showNotification('请至少设置一个搜索条件', 'warning');
        return;
    }
    
    try {
        const results = await api.get('/api/search/advanced', searchParams);
        
        closeModal();
        
        // 显示搜索结果页面
        showSearchResultsPage(results.files || [], searchParams);
        
    } catch (error) {
        console.error('高级搜索失败:', error);
        app.showNotification('搜索失败: ' + error.message, 'error');
    }
}

// 显示搜索结果页面
function showSearchResultsPage(results, searchParams) {
    // 保存当前状态
    const originalContent = document.querySelector('.content-area').innerHTML;
    
    // 创建搜索结果页面
    const searchResultsHTML = `
        <div class="search-results-page">
            <div class="search-results-header">
                <h2>搜索结果 (${results.length} 个文件)</h2>
                <button class="btn btn-secondary" onclick="returnToFileList()">返回文件列表</button>
            </div>
            
            <div class="search-params">
                <h3>搜索条件:</h3>
                <div class="search-params-list">
                    ${Object.entries(searchParams).map(([key, value]) => `
                        <span class="search-param-tag">${getSearchParamLabel(key)}: ${value}</span>
                    `).join('')}
                </div>
            </div>
            
            <div class="search-results-grid">
                ${results.length === 0 ? 
                    '<div class="empty-state"><div class="empty-icon">🔍</div><h3>未找到匹配的文件</h3></div>' :
                    results.map(file => app.createFileItem(file)).join('')
                }
            </div>
        </div>
    `;
    
    document.querySelector('.content-area').innerHTML = searchResultsHTML;
    
    // 保存原始内容以便返回
    window.originalFileListContent = originalContent;
    
    // 添加文件项事件监听器
    app.attachFileItemListeners();
}

// 返回文件列表
function returnToFileList() {
    if (window.originalFileListContent) {
        document.querySelector('.content-area').innerHTML = window.originalFileListContent;
        app.attachFileItemListeners();
        delete window.originalFileListContent;
    }
}

// 获取搜索参数标签
function getSearchParamLabel(key) {
    const labels = {
        filename: '文件名',
        filetype: '文件类型',
        size_min: '最小大小',
        size_max: '最大大小',
        date_from: '开始日期',
        date_to: '结束日期',
        uploader: '上传者',
        include_content: '包含内容'
    };
    
    return labels[key] || key;
}

// 搜索历史功能
function saveSearchHistory(query, engine) {
    const history = getSearchHistory();
    const newEntry = {
        query,
        engine,
        timestamp: Date.now()
    };
    
    // 移除重复项
    const filtered = history.filter(item => 
        !(item.query === query && item.engine === engine)
    );
    
    // 添加到开头
    filtered.unshift(newEntry);
    
    // 只保留最近20条
    const limited = filtered.slice(0, 20);
    
    localStorage.setItem('search_history', JSON.stringify(limited));
}

// 获取搜索历史
function getSearchHistory() {
    try {
        return JSON.parse(localStorage.getItem('search_history') || '[]');
    } catch {
        return [];
    }
}

// 显示搜索历史
function showSearchHistory() {
    const history = getSearchHistory();
    
    if (history.length === 0) {
        app.showNotification('暂无搜索历史', 'info');
        return;
    }
    
    const modal = createModal('搜索历史', `
        <div class="search-history-list">
            ${history.map((item, index) => `
                <div class="search-history-item" onclick="useSearchHistory(${index})">
                    <div class="search-history-query">${item.query}</div>
                    <div class="search-history-meta">
                        <span class="search-history-engine">${item.engine === 'filename' ? '文件名搜索' : '图片搜索'}</span>
                        <span class="search-history-time">${formatDate(new Date(item.timestamp))}</span>
                    </div>
                </div>
            `).join('')}
        </div>
    `, [
        {
            text: '清空历史',
            class: 'btn-secondary',
            onclick: () => clearSearchHistory()
        },
        {
            text: '关闭',
            class: 'btn-primary',
            onclick: () => closeModal()
        }
    ]);
    
    showModal(modal);
}

// 使用搜索历史
function useSearchHistory(index) {
    const history = getSearchHistory();
    const item = history[index];
    
    if (item) {
        switchSearchEngine(item.engine);
        document.getElementById('search-input').value = item.query;
        performSearch(item.query);
    }
    
    closeModal();
}

// 清空搜索历史
function clearSearchHistory() {
    localStorage.removeItem('search_history');
    closeModal();
    app.showNotification('搜索历史已清空', 'success');
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        switchSearchEngine,
        performSearch,
        displaySearchResults,
        hideSearchResults,
        selectSearchResult,
        showAdvancedSearch,
        resetAdvancedSearch,
        performAdvancedSearch,
        showSearchResultsPage,
        returnToFileList,
        getSearchParamLabel,
        saveSearchHistory,
        getSearchHistory,
        showSearchHistory,
        useSearchHistory,
        clearSearchHistory
    };
}
