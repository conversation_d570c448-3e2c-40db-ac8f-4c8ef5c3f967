"""
搜索功能路由
"""
from fastapi import APIRouter, Depends, Request, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel

from app.database import get_db
from app.middleware.auth import get_current_user_id

router = APIRouter()


class SearchResult(BaseModel):
    id: int
    filename: str
    file_type: str
    file_size: int
    similarity: Optional[float] = None


@router.get("/files", response_model=List[SearchResult])
async def search_files(
    request: Request,
    q: str = Query(..., description="搜索关键词"),
    engine: str = Query("filename", description="搜索引擎类型: filename 或 image"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
):
    """搜索文件"""
    # TODO: 实现文件搜索逻辑
    return []


@router.post("/image")
async def search_by_image(
    request: Request,
    # TODO: 添加图片上传参数
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
):
    """以图搜图"""
    # TODO: 实现图像搜索逻辑
    return {"message": "图像搜索功能待实现"}
