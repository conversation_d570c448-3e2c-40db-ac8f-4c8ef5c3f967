# -*- coding: utf-8 -*-
"""
Simple HTTP Server for Frontend Development
前端开发用的简单HTTP服务器
"""
import os
import sys
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """支持CORS的HTTP请求处理器"""
    
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()
    
    def do_GET(self):
        # 处理根路径，重定向到index.html
        if self.path == '/':
            self.path = '/index.html'
        
        # 处理API请求（模拟）
        if self.path.startswith('/api/'):
            self.handle_api_request()
            return
        
        # 处理静态文件
        super().do_GET()
    
    def handle_api_request(self):
        """处理API请求（模拟响应）"""
        parsed_url = urlparse(self.path)
        path = parsed_url.path

        # 模拟API响应
        if path == '/api/auth/me':
            response = {
                "error": "Unauthorized",
                "message": "请先登录"
            }
            self.send_json_response(response, 401)

        elif path == '/api/auth/login':
            response = {
                "access_token": "mock_user_token",
                "refresh_token": "mock_refresh_token",
                "user_info": {
                    "id": 1,
                    "username": "testuser",
                    "email": "<EMAIL>",
                    "full_name": "测试用户",
                    "role": "user",
                    "created_at": "2024-01-01T00:00:00Z"
                }
            }
            self.send_json_response(response)

        elif path == '/api/auth/register':
            response = {
                "message": "注册成功",
                "user_id": 2
            }
            self.send_json_response(response)

        elif path == '/api/admin/login':
            response = {
                "access_token": "mock_admin_token",
                "refresh_token": "mock_admin_refresh_token",
                "user_info": {
                    "id": 1,
                    "username": "admin",
                    "email": "<EMAIL>",
                    "full_name": "系统管理员",
                    "role": "admin",
                    "created_at": "2024-01-01T00:00:00Z"
                }
            }
            self.send_json_response(response)

        elif path == '/api/admin/verify':
            response = {
                "valid": True,
                "user": {
                    "username": "admin",
                    "role": "admin"
                }
            }
            self.send_json_response(response)

        elif path == '/api/files/':
            response = {
                "files": [],
                "total": 0,
                "page": 1,
                "size": 20,
                "path": []
            }
            self.send_json_response(response)

        elif path == '/api/system/stats':
            response = {
                "total_files": 0,
                "online_users": 1,
                "storage_used": 0
            }
            self.send_json_response(response)

        elif path == '/api/system/health':
            response = {
                "status": "healthy",
                "database": "not_connected",
                "redis": "not_connected"
            }
            self.send_json_response(response)

        else:
            response = {
                "error": "Not Found",
                "message": "API endpoint not found"
            }
            self.send_json_response(response, 404)
    
    def send_json_response(self, data, status_code=200):
        """发送JSON响应"""
        import json
        
        response_data = json.dumps(data, ensure_ascii=False, indent=2)
        
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.end_headers()
        self.wfile.write(response_data.encode('utf-8'))

def main():
    """主函数"""
    # 切换到前端目录
    frontend_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(frontend_dir)
    
    PORT = 3000
    
    print("=" * 60)
    print("Frontend Development Server")
    print("前端开发服务器")
    print("=" * 60)
    print(f"Server running at: http://localhost:{PORT}")
    print(f"服务器运行地址: http://localhost:{PORT}")
    print("Press Ctrl+C to stop")
    print("按 Ctrl+C 停止服务")
    print("=" * 60)
    
    try:
        with socketserver.TCPServer(("", PORT), CORSHTTPRequestHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped.")
        print("服务器已停止。")

if __name__ == "__main__":
    main()
