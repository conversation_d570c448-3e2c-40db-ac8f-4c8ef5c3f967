"""
认证中间件
"""
import time
from typing import Optional
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from jose import JWTError, jwt
from loguru import logger

from app.config import get_settings
from app.database import get_db, cache_manager, CacheKeys
from app.models.user import User

settings = get_settings()

# 不需要认证的路径
EXEMPT_PATHS = [
    "/",
    "/health",
    "/docs",
    "/redoc",
    "/openapi.json",
    "/api/auth/login",
    "/api/auth/register",
    "/static",
    "/favicon.ico"
]


class AuthMiddleware(BaseHTTPMiddleware):
    """认证中间件"""
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        start_time = time.time()
        
        # 检查是否需要认证
        if self.is_exempt_path(request.url.path):
            response = await call_next(request)
            return response
        
        # 获取认证信息
        try:
            user = await self.authenticate_request(request)
            if user:
                request.state.user = user
                request.state.user_id = user.id
            else:
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={"error": True, "message": "未授权访问"}
                )
        except HTTPException as e:
            return JSONResponse(
                status_code=e.status_code,
                content={"error": True, "message": e.detail}
            )
        except Exception as e:
            logger.error(f"认证中间件错误: {e}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"error": True, "message": "认证服务错误"}
            )
        
        # 继续处理请求
        response = await call_next(request)
        
        # 记录请求处理时间
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        
        return response
    
    def is_exempt_path(self, path: str) -> bool:
        """检查路径是否免于认证"""
        for exempt_path in EXEMPT_PATHS:
            if path.startswith(exempt_path):
                return True
        return False
    
    async def authenticate_request(self, request: Request) -> Optional[User]:
        """认证请求"""
        # 获取Authorization头
        authorization = request.headers.get("Authorization")
        if not authorization:
            return None
        
        # 检查Bearer token格式
        try:
            scheme, token = authorization.split()
            if scheme.lower() != "bearer":
                return None
        except ValueError:
            return None
        
        # 验证JWT token
        try:
            payload = jwt.decode(
                token, 
                settings.SECRET_KEY, 
                algorithms=[settings.ALGORITHM]
            )
            user_id: int = payload.get("sub")
            if user_id is None:
                return None
        except JWTError:
            return None
        
        # 检查token是否在缓存中（会话管理）
        session_key = CacheKeys.user_session(user_id)
        cached_token = await cache_manager.get(session_key)
        if cached_token != token:
            return None
        
        # 获取用户信息
        user = await self.get_user(user_id)
        if not user or not user.is_active:
            return None
        
        # 检查用户是否被锁定
        if user.is_locked():
            raise HTTPException(
                status_code=status.HTTP_423_LOCKED,
                detail="账户已被锁定"
            )
        
        # 检查网络访问权限
        client_ip = self.get_client_ip(request)
        if not await self.check_network_access(user, client_ip):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="网络访问被拒绝"
            )
        
        return user
    
    async def get_user(self, user_id: int) -> Optional[User]:
        """获取用户信息"""
        # 先从缓存获取
        cache_key = f"user:{user_id}"
        cached_user = await cache_manager.get(cache_key)
        if cached_user:
            # 这里应该反序列化用户对象，简化处理
            pass
        
        # 从数据库获取
        db = next(get_db())
        try:
            user = db.query(User).filter(User.id == user_id).first()
            if user:
                # 缓存用户信息
                await cache_manager.set(cache_key, str(user.id), expire=3600)
            return user
        finally:
            db.close()
    
    def get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 返回直连IP
        return request.client.host if request.client else "unknown"
    
    async def check_network_access(self, user: User, client_ip: str) -> bool:
        """检查网络访问权限"""
        # 检查是否为内网IP
        is_internal = self.is_internal_ip(client_ip)
        
        # 内网访问检查
        if is_internal and not user.internal_access:
            return False
        
        # 外网访问检查
        if not is_internal and not user.external_access:
            return False
        
        # IP白名单检查
        if user.ip_whitelist:
            try:
                import json
                whitelist = json.loads(user.ip_whitelist)
                if whitelist and client_ip not in whitelist:
                    return False
            except (json.JSONDecodeError, TypeError):
                pass
        
        return True
    
    def is_internal_ip(self, ip: str) -> bool:
        """检查是否为内网IP"""
        import ipaddress
        
        try:
            ip_obj = ipaddress.ip_address(ip)
            
            # 检查是否为私有IP
            if ip_obj.is_private:
                return True
            
            # 检查是否在配置的内网范围内
            for network in settings.INTERNAL_NETWORKS:
                if ip_obj in ipaddress.ip_network(network):
                    return True
            
            return False
        except ValueError:
            # IP地址格式错误，默认为外网
            return False


def get_current_user(request: Request) -> User:
    """获取当前用户"""
    if not hasattr(request.state, "user"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未授权访问"
        )
    return request.state.user


def get_current_user_id(request: Request) -> int:
    """获取当前用户ID"""
    if not hasattr(request.state, "user_id"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未授权访问"
        )
    return request.state.user_id


def require_admin(request: Request) -> User:
    """要求管理员权限"""
    user = get_current_user(request)
    if not user.is_admin():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return user


def require_active_user(request: Request) -> User:
    """要求活跃用户"""
    user = get_current_user(request)
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="账户未激活"
        )
    return user
