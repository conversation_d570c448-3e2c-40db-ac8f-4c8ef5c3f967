<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业级文件共享系统 - UI原稿</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .notification-banner {
            background: rgba(255,255,255,0.1);
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 14px;
            animation: scroll-left 30s linear infinite;
            white-space: nowrap;
            overflow: hidden;
        }
        
        @keyframes scroll-left {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
        
        .main-container {
            display: flex;
            height: calc(100vh - 120px);
        }
        
        .sidebar {
            width: 280px;
            background: white;
            border-right: 1px solid #e0e0e0;
            padding: 20px;
            overflow-y: auto;
        }
        
        .search-section {
            margin-bottom: 25px;
        }
        
        .search-engines {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .search-engine {
            flex: 1;
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s;
        }
        
        .search-engine.active {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .search-box {
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 12px 40px 12px 15px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s;
        }
        
        .search-input:focus {
            border-color: #667eea;
        }
        
        .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: #667eea;
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            color: white;
            cursor: pointer;
        }
        
        .folder-tree {
            margin-bottom: 25px;
        }
        
        .tree-item {
            padding: 8px 0;
            cursor: pointer;
            border-left: 3px solid transparent;
            padding-left: 15px;
            transition: all 0.3s;
        }
        
        .tree-item:hover {
            background: #f0f0f0;
            border-left-color: #667eea;
        }
        
        .tree-item.active {
            background: #e8f0fe;
            border-left-color: #667eea;
            color: #667eea;
        }
        
        .stats-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }
        
        .toolbar {
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .view-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .view-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }
        
        .view-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        
        .file-grid {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .file-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }
        
        .file-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .file-item.selected {
            border-color: #667eea;
            background: #e8f0fe;
        }
        
        .file-thumbnail {
            width: 100%;
            height: 120px;
            background: #f8f9fa;
            border-radius: 5px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #6c757d;
        }
        
        .file-name {
            font-size: 14px;
            margin-bottom: 5px;
            word-break: break-all;
        }
        
        .file-info {
            font-size: 12px;
            color: #6c757d;
        }
        
        .permission-badge {
            position: absolute;
            top: 5px;
            right: 5px;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            color: white;
        }
        
        .permission-read {
            background: #17a2b8;
        }
        
        .permission-write {
            background: #28a745;
        }
        
        .permission-admin {
            background: #dc3545;
        }
        
        .status-bar {
            padding: 10px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #6c757d;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s;
        }
        
        .admin-panel {
            position: fixed;
            top: 0;
            right: -400px;
            width: 400px;
            height: 100%;
            background: white;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            transition: right 0.3s;
            z-index: 999;
            overflow-y: auto;
        }
        
        .admin-panel.open {
            right: 0;
        }
        
        .admin-header {
            padding: 20px;
            background: #667eea;
            color: white;
        }
        
        .admin-content {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 10px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
    </style>
</head>
<body>
    <!-- 头部区域 -->
    <header class="header">
        <div class="header-top">
            <div class="logo">🗂️ 企业文件共享系统</div>
            <div class="user-info">
                <span>在线用户: 15</span>
                <span>|</span>
                <span>管理员</span>
                <button class="btn btn-secondary" onclick="toggleAdminPanel()">管理面板</button>
                <button class="btn btn-warning">退出</button>
            </div>
        </div>
        <div class="notification-banner">
            📢 系统通知：新版本已发布，增加了图像识别搜索功能，欢迎体验！ | 今日下载量：1,234 | 存储总量：2.5TB
        </div>
    </header>

    <div class="main-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <!-- 搜索区域 -->
            <div class="search-section">
                <h3>搜索引擎</h3>
                <div class="search-engines">
                    <div class="search-engine active">文件名</div>
                    <div class="search-engine">识图</div>
                </div>
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="搜索文件...">
                    <button class="search-btn">🔍</button>
                </div>
            </div>

            <!-- 文件夹树 -->
            <div class="folder-tree">
                <h3>文件夹</h3>
                <div class="tree-item active">📁 设计素材</div>
                <div class="tree-item">📁 产品图片</div>
                <div class="tree-item">📁 营销资料</div>
                <div class="tree-item">📁 技术文档</div>
                <div class="tree-item">📁 视频资源</div>
            </div>

            <!-- 统计面板 -->
            <div class="stats-panel">
                <h4>系统统计</h4>
                <div class="stat-item">
                    <span>总文件数:</span>
                    <span>12,456</span>
                </div>
                <div class="stat-item">
                    <span>图片文件:</span>
                    <span>8,234</span>
                </div>
                <div class="stat-item">
                    <span>今日下载:</span>
                    <span>1,234</span>
                </div>
                <div class="stat-item">
                    <span>今日上传:</span>
                    <span>56</span>
                </div>
                <div class="stat-item">
                    <span>存储使用:</span>
                    <span>2.5TB/5TB</span>
                </div>
            </div>
        </aside>

        <!-- 主内容区域 -->
        <main class="content-area">
            <!-- 工具栏 -->
            <div class="toolbar">
                <div class="view-controls">
                    <span>视图:</span>
                    <button class="view-btn">超大图标</button>
                    <button class="view-btn active">大图标</button>
                    <button class="view-btn">中等图标</button>
                    <button class="view-btn">详情</button>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-success">📤 上传</button>
                    <button class="btn btn-primary">📥 下载选中</button>
                    <button class="btn btn-secondary">📦 打包下载</button>
                </div>
            </div>

            <!-- 文件网格 -->
            <div class="file-grid">
                <div class="file-item selected">
                    <div class="permission-badge permission-read">只读</div>
                    <div class="file-thumbnail">🖼️</div>
                    <div class="file-name">产品展示图.jpg</div>
                    <div class="file-info">2.5MB | 2024-01-15</div>
                </div>
                
                <div class="file-item">
                    <div class="permission-badge permission-write">读写</div>
                    <div class="file-thumbnail">🎨</div>
                    <div class="file-name">设计稿.psd</div>
                    <div class="file-info">15.2MB | 2024-01-14</div>
                </div>
                
                <div class="file-item">
                    <div class="permission-badge permission-admin">管理</div>
                    <div class="file-thumbnail">📄</div>
                    <div class="file-name">技术文档.pdf</div>
                    <div class="file-info">1.8MB | 2024-01-13</div>
                </div>
                
                <div class="file-item">
                    <div class="permission-badge permission-read">只读</div>
                    <div class="file-thumbnail">🖼️</div>
                    <div class="file-name">LOGO设计.ai</div>
                    <div class="file-info">5.6MB | 2024-01-12</div>
                </div>
                
                <div class="file-item">
                    <div class="permission-badge permission-write">读写</div>
                    <div class="file-thumbnail">🖼️</div>
                    <div class="file-name">宣传海报.eps</div>
                    <div class="file-info">8.9MB | 2024-01-11</div>
                </div>
                
                <div class="file-item">
                    <div class="permission-badge permission-read">只读</div>
                    <div class="file-thumbnail">🖼️</div>
                    <div class="file-name">产品照片.tif</div>
                    <div class="file-info">12.3MB | 2024-01-10</div>
                </div>
            </div>
        </main>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
        <div>已选择 1 个文件 | 总计 2.5MB</div>
        <div>网络状态: 内网+外网 | 服务器状态: 正常</div>
    </div>

    <!-- 管理面板 -->
    <div class="admin-panel" id="adminPanel">
        <div class="admin-header">
            <h3>管理面板</h3>
            <button onclick="toggleAdminPanel()" style="float: right; background: none; border: none; color: white; font-size: 20px;">×</button>
        </div>
        <div class="admin-content">
            <div class="form-group">
                <label class="form-label">用户权限设置</label>
                <select class="form-control">
                    <option>选择用户</option>
                    <option>张三 - 设计师</option>
                    <option>李四 - 产品经理</option>
                    <option>王五 - 开发者</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">权限类型</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="read"> <label for="read">读取</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="write"> <label for="write">写入</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="delete"> <label for="delete">删除</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="download"> <label for="download">下载</label>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">网络访问</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="internal" checked> <label for="internal">内网</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="external"> <label for="external">外网</label>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">下载限制</label>
                <input type="number" class="form-control" placeholder="单次下载文件数限制" value="10">
            </div>
            
            <div class="form-group">
                <label class="form-label">打包下载大小限制(MB)</label>
                <input type="number" class="form-control" placeholder="单次打包大小限制" value="500">
            </div>
            
            <button class="btn btn-primary" style="width: 100%;">保存设置</button>
        </div>
    </div>

    <!-- 下载进度模态框 -->
    <div class="modal" id="downloadModal">
        <div class="modal-content">
            <h3>文件下载中...</h3>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 45%;"></div>
            </div>
            <p>正在下载: 产品展示图.jpg (45%)</p>
            <div style="text-align: center; margin-top: 20px;">
                <button class="btn btn-secondary">取消下载</button>
            </div>
        </div>
    </div>

    <script>
        function toggleAdminPanel() {
            const panel = document.getElementById('adminPanel');
            panel.classList.toggle('open');
        }
        
        // 文件选择功能
        document.querySelectorAll('.file-item').forEach(item => {
            item.addEventListener('click', function() {
                this.classList.toggle('selected');
            });
        });
        
        // 搜索引擎切换
        document.querySelectorAll('.search-engine').forEach(engine => {
            engine.addEventListener('click', function() {
                document.querySelectorAll('.search-engine').forEach(e => e.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // 视图切换
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
