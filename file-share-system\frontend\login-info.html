<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录信息 - 企业级文件共享系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .info-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 600px;
            margin: 20px;
        }

        .info-header {
            background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .info-icon {
            font-size: 3em;
            margin-bottom: 10px;
            display: block;
        }

        .info-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .info-subtitle {
            opacity: 0.9;
            font-size: 1em;
        }

        .info-content {
            padding: 40px 30px;
        }

        .account-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }

        .account-section.admin {
            border-left-color: #ff6b6b;
        }

        .account-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .account-details {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .credential-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }

        .credential-item:last-child {
            border-bottom: none;
        }

        .credential-label {
            font-weight: 500;
            color: #666;
        }

        .credential-value {
            font-family: 'Courier New', monospace;
            background: #f1f3f4;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: #333;
        }

        .login-buttons {
            display: flex;
            gap: 15px;
            margin-top: 15px;
        }

        .login-btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            text-align: center;
            display: block;
        }

        .user-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .user-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .admin-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
        }

        .admin-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 107, 107, 0.3);
        }

        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
            color: #856404;
        }

        .note-icon {
            color: #f39c12;
            margin-right: 5px;
        }

        @media (max-width: 480px) {
            .info-container {
                margin: 10px;
            }
            
            .info-header {
                padding: 25px 20px;
            }
            
            .info-content {
                padding: 30px 20px;
            }
            
            .login-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="info-container">
        <div class="info-header">
            <span class="info-icon">🔐</span>
            <h1 class="info-title">系统登录信息</h1>
            <p class="info-subtitle">Enterprise File Sharing System</p>
        </div>

        <div class="info-content">
            <!-- 用户账户信息 -->
            <div class="account-section">
                <div class="account-title">
                    <span>👥</span>
                    普通用户账户
                </div>
                
                <div class="account-details">
                    <div class="credential-item">
                        <span class="credential-label">登录方式:</span>
                        <span class="credential-value">任意用户名密码</span>
                    </div>
                    <div class="credential-item">
                        <span class="credential-label">示例用户名:</span>
                        <span class="credential-value">user1</span>
                    </div>
                    <div class="credential-item">
                        <span class="credential-label">示例密码:</span>
                        <span class="credential-value">123456</span>
                    </div>
                    <div class="credential-item">
                        <span class="credential-label">权限:</span>
                        <span class="credential-value">文件管理</span>
                    </div>
                </div>
                
                <div class="login-buttons">
                    <a href="user-login.html" class="login-btn user-btn">用户登录</a>
                </div>
            </div>

            <!-- 管理员账户信息 -->
            <div class="account-section admin">
                <div class="account-title">
                    <span>👨‍💼</span>
                    管理员账户
                </div>
                
                <div class="account-details">
                    <div class="credential-item">
                        <span class="credential-label">用户名:</span>
                        <span class="credential-value">admin</span>
                    </div>
                    <div class="credential-item">
                        <span class="credential-label">密码:</span>
                        <span class="credential-value">admin123</span>
                    </div>
                    <div class="credential-item">
                        <span class="credential-label">权限:</span>
                        <span class="credential-value">系统管理</span>
                    </div>
                </div>
                
                <div class="login-buttons">
                    <a href="admin-login.html" class="login-btn admin-btn">管理员登录</a>
                </div>
            </div>

            <!-- 注意事项 -->
            <div class="note">
                <span class="note-icon">💡</span>
                <strong>注意事项：</strong>
                <ul style="margin-top: 10px; padding-left: 20px;">
                    <li>这是演示系统，使用模拟的登录验证</li>
                    <li>普通用户可以使用任意用户名和密码登录</li>
                    <li>管理员必须使用指定的用户名和密码</li>
                    <li>不同角色有不同的界面和权限</li>
                    <li>生产环境中应使用真实的数据库验证</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
