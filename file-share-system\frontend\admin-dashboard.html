<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员控制台 - 企业级文件共享系统</title>
    <link rel="stylesheet" href="static/css/main.css">
    <link rel="stylesheet" href="static/css/components.css">
    <style>
        .admin-header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        
        .admin-nav {
            background: #2c3e50;
            color: white;
        }
        
        .admin-nav .nav-container {
            background: none;
        }
        
        .admin-brand {
            color: #ff6b6b;
        }
        
        .admin-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #ff6b6b;
        }
        
        .stat-icon {
            font-size: 2.5em;
            margin-bottom: 10px;
            display: block;
        }
        
        .stat-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #333;
        }
        
        .admin-tabs {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .tab-headers {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .tab-header {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            color: #666;
        }
        
        .tab-header.active {
            background: white;
            color: #ff6b6b;
            border-bottom: 2px solid #ff6b6b;
        }
        
        .tab-content {
            padding: 30px;
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .admin-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .admin-table th,
        .admin-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .admin-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .admin-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .admin-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- 管理员导航栏 -->
    <nav class="navbar admin-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <span class="logo">🛡️</span>
                <span class="brand-text admin-brand">管理员控制台</span>
            </div>
            
            <div class="user-menu">
                <div class="user-info" id="admin-info">
                    <span class="user-avatar">👨‍💼</span>
                    <span class="user-name">管理员</span>
                </div>
                <div class="user-dropdown" id="admin-dropdown">
                    <a href="index.html">用户界面</a>
                    <a href="#" id="admin-logout">退出登录</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <div class="content-area" style="width: 100%; padding: 30px;">
            <!-- 统计卡片 -->
            <div class="admin-stats">
                <div class="stat-card">
                    <span class="stat-icon">👥</span>
                    <div class="stat-title">总用户数</div>
                    <div class="stat-value" id="total-users">0</div>
                </div>
                
                <div class="stat-card">
                    <span class="stat-icon">📁</span>
                    <div class="stat-title">总文件数</div>
                    <div class="stat-value" id="total-files">0</div>
                </div>
                
                <div class="stat-card">
                    <span class="stat-icon">💾</span>
                    <div class="stat-title">存储使用</div>
                    <div class="stat-value" id="storage-usage">0 GB</div>
                </div>
                
                <div class="stat-card">
                    <span class="stat-icon">🌐</span>
                    <div class="stat-title">在线用户</div>
                    <div class="stat-value" id="online-users">0</div>
                </div>
            </div>

            <!-- 管理标签页 -->
            <div class="admin-tabs">
                <div class="tab-headers">
                    <button class="tab-header active" onclick="switchAdminTab('users')">用户管理</button>
                    <button class="tab-header" onclick="switchAdminTab('files')">文件管理</button>
                    <button class="tab-header" onclick="switchAdminTab('logs')">活动日志</button>
                    <button class="tab-header" onclick="switchAdminTab('settings')">系统设置</button>
                </div>

                <!-- 用户管理 -->
                <div id="users-tab" class="tab-content active">
                    <div class="admin-actions">
                        <button class="btn btn-primary" onclick="showAddUserModal()">
                            <span class="btn-icon">➕</span>
                            添加用户
                        </button>
                        <button class="btn btn-secondary" onclick="refreshUsers()">
                            <span class="btn-icon">🔄</span>
                            刷新
                        </button>
                    </div>
                    
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>邮箱</th>
                                <th>真实姓名</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>注册时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="users-table-body">
                            <tr>
                                <td colspan="7" style="text-align: center; padding: 40px;">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 文件管理 -->
                <div id="files-tab" class="tab-content">
                    <div class="admin-actions">
                        <button class="btn btn-secondary" onclick="refreshFiles()">
                            <span class="btn-icon">🔄</span>
                            刷新
                        </button>
                        <button class="btn btn-secondary" onclick="cleanupFiles()">
                            <span class="btn-icon">🗑️</span>
                            清理无效文件
                        </button>
                    </div>
                    
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>文件名</th>
                                <th>大小</th>
                                <th>类型</th>
                                <th>上传者</th>
                                <th>上传时间</th>
                                <th>下载次数</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="files-table-body">
                            <tr>
                                <td colspan="7" style="text-align: center; padding: 40px;">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 活动日志 -->
                <div id="logs-tab" class="tab-content">
                    <div class="admin-actions">
                        <button class="btn btn-secondary" onclick="refreshLogs()">
                            <span class="btn-icon">🔄</span>
                            刷新
                        </button>
                        <button class="btn btn-secondary" onclick="exportLogs()">
                            <span class="btn-icon">📥</span>
                            导出日志
                        </button>
                    </div>
                    
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>用户</th>
                                <th>操作</th>
                                <th>详情</th>
                                <th>IP地址</th>
                            </tr>
                        </thead>
                        <tbody id="logs-table-body">
                            <tr>
                                <td colspan="5" style="text-align: center; padding: 40px;">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 系统设置 -->
                <div id="settings-tab" class="tab-content">
                    <div class="settings-form">
                        <h3>系统配置</h3>
                        
                        <div class="form-group">
                            <label class="form-label">系统名称</label>
                            <input type="text" class="form-input" value="企业级文件共享系统">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">最大文件大小 (MB)</label>
                            <input type="number" class="form-input" value="100">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">允许的文件类型</label>
                            <textarea class="form-input form-textarea">jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar</textarea>
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" checked> 启用用户注册
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" checked> 启用文件加密
                            </label>
                        </div>
                        
                        <button class="btn btn-primary">保存设置</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="static/js/utils.js"></script>
    <script src="static/js/ui-components.js"></script>
    <script>
        // 管理员API客户端
        class AdminAPI {
            constructor() {
                this.baseURL = 'http://localhost:8000';
                this.token = localStorage.getItem('admin_token');
            }

            async request(endpoint, options = {}) {
                const url = `${this.baseURL}${endpoint}`;
                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.token}`
                    },
                    ...options
                };

                const response = await fetch(url, config);
                
                if (response.status === 401) {
                    localStorage.removeItem('admin_token');
                    window.location.href = 'admin-login.html';
                    return;
                }

                return response.json();
            }

            async getStats() {
                return this.request('/api/admin/stats');
            }

            async getUsers() {
                return this.request('/api/admin/users');
            }

            async getFiles() {
                return this.request('/api/admin/files');
            }

            async getLogs() {
                return this.request('/api/admin/logs');
            }
        }

        const adminAPI = new AdminAPI();

        // 切换管理标签页
        function switchAdminTab(tabName) {
            // 更新标签按钮状态
            document.querySelectorAll('.tab-header').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 更新内容显示
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tabName + '-tab').classList.add('active');
        }

        // 管理员退出登录
        document.getElementById('admin-logout').addEventListener('click', () => {
            localStorage.removeItem('admin_token');
            localStorage.removeItem('admin_refresh_token');
            localStorage.removeItem('user_role');
            window.location.href = 'admin-login.html';
        });

        // 切换管理员下拉菜单
        document.getElementById('admin-info').addEventListener('click', () => {
            const dropdown = document.getElementById('admin-dropdown');
            const isVisible = dropdown.style.display === 'block';
            dropdown.style.display = isVisible ? 'none' : 'block';
        });

        // 页面加载时检查权限
        window.addEventListener('load', () => {
            const adminToken = localStorage.getItem('admin_token');
            const userRole = localStorage.getItem('user_role');
            
            if (!adminToken || userRole !== 'admin') {
                window.location.href = 'admin-login.html';
                return;
            }
            
            // 加载管理员数据
            loadAdminData();
        });

        // 加载管理员数据
        async function loadAdminData() {
            try {
                // 模拟数据
                document.getElementById('total-users').textContent = '156';
                document.getElementById('total-files').textContent = '2,847';
                document.getElementById('storage-usage').textContent = '45.2 GB';
                document.getElementById('online-users').textContent = '23';
                
                // 加载用户列表
                loadUsers();
            } catch (error) {
                console.error('加载管理员数据失败:', error);
            }
        }

        // 加载用户列表
        function loadUsers() {
            const tbody = document.getElementById('users-table-body');
            tbody.innerHTML = `
                <tr>
                    <td>admin</td>
                    <td><EMAIL></td>
                    <td>系统管理员</td>
                    <td>管理员</td>
                    <td><span class="status-badge status-active">活跃</span></td>
                    <td>2024-01-01</td>
                    <td>
                        <button class="btn btn-sm btn-secondary">编辑</button>
                        <button class="btn btn-sm btn-danger">禁用</button>
                    </td>
                </tr>
                <tr>
                    <td>user1</td>
                    <td><EMAIL></td>
                    <td>张三</td>
                    <td>用户</td>
                    <td><span class="status-badge status-active">活跃</span></td>
                    <td>2024-01-15</td>
                    <td>
                        <button class="btn btn-sm btn-secondary">编辑</button>
                        <button class="btn btn-sm btn-danger">禁用</button>
                    </td>
                </tr>
            `;
        }

        // 占位函数
        function showAddUserModal() { alert('添加用户功能开发中'); }
        function refreshUsers() { loadUsers(); }
        function refreshFiles() { alert('刷新文件功能开发中'); }
        function cleanupFiles() { alert('清理文件功能开发中'); }
        function refreshLogs() { alert('刷新日志功能开发中'); }
        function exportLogs() { alert('导出日志功能开发中'); }
    </script>
</body>
</html>
