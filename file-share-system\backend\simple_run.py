# -*- coding: utf-8 -*-
"""
Simple Application Startup Script
简化的应用启动脚本
"""
import os
import sys

# Set UTF-8 encoding for Windows
if sys.platform.startswith('win'):
    import locale
    try:
        locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'C.UTF-8')
        except:
            pass

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """Main function"""
    print("=" * 50)
    print("Enterprise File Sharing System")
    print("企业级文件共享系统")
    print("=" * 50)
    
    try:
        # Import after setting up encoding
        import uvicorn
        from app.config import get_settings
        
        settings = get_settings()
        
        print(f"Starting server on http://{settings.HOST}:{settings.PORT}")
        print(f"服务启动地址: http://{settings.HOST}:{settings.PORT}")
        print(f"API Documentation: http://{settings.HOST}:{settings.PORT}/docs")
        print(f"API文档地址: http://{settings.HOST}:{settings.PORT}/docs")
        print("=" * 50)
        
        # Start the server
        uvicorn.run(
            "app.main:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.DEBUG,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"Import Error: {e}")
        print("Please install required packages:")
        print("请安装必要的依赖包:")
        print("pip install fastapi uvicorn sqlalchemy pymysql redis python-jose passlib loguru")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting server: {e}")
        print(f"服务启动错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
