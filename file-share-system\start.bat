@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    Enterprise File Sharing System
echo    启动脚本 / Startup Script
echo ========================================
echo.

cd /d "%~dp0"

echo [1/5] Checking Python Environment / 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not installed or not in PATH
    echo 错误: Python未安装或未添加到PATH
    echo Please install Python 3.8+ and add to system PATH
    echo 请安装Python 3.8+并添加到系统PATH
    pause
    exit /b 1
)
echo OK: Python environment ready / Python环境正常

echo.
echo [2/5] 检查MySQL服务...
sc query mysql >nul 2>&1
if errorlevel 1 (
    echo ⚠️  MySQL服务未启动，尝试启动...
    net start mysql >nul 2>&1
    if errorlevel 1 (
        echo ❌ 无法启动MySQL服务
        echo 请手动启动MySQL服务
        pause
        exit /b 1
    )
)
echo ✅ MySQL服务正常

echo.
echo [3/5] 检查Redis服务...
redis-cli ping >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Redis服务未启动
    echo 请手动启动Redis服务
    pause
    exit /b 1
)
echo ✅ Redis服务正常

echo.
echo [4/5] 安装Python依赖...
cd backend
if not exist "venv" (
    echo 创建虚拟环境...
    python -m venv venv
)

call venv\Scripts\activate.bat
pip install -r requirements.txt >nul 2>&1
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)
echo ✅ 依赖安装完成

echo.
echo [5/5] 初始化数据库...
python init_db.py
if errorlevel 1 (
    echo ❌ 数据库初始化失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo    🎉 系统启动中...
echo ========================================
echo.
echo 📍 前端页面: http://localhost:8000/static/index.html
echo 📚 API文档:  http://localhost:8000/docs
echo 🔍 健康检查: http://localhost:8000/health
echo.
echo 👤 默认管理员账户:
echo    用户名: admin
echo    密码:   admin123
echo.
echo 按 Ctrl+C 停止服务
echo ========================================
echo.

python run.py
