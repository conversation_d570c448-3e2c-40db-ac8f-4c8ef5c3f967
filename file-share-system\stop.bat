@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    企业级文件共享系统 - 停止脚本
echo ========================================
echo.

echo 正在停止文件共享系统服务...

:: 查找并终止Python进程
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo table /nh ^| findstr "python.exe"') do (
    echo 终止进程 %%i
    taskkill /pid %%i /f >nul 2>&1
)

:: 查找并终止uvicorn进程
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq uvicorn.exe" /fo table /nh ^| findstr "uvicorn.exe"') do (
    echo 终止进程 %%i
    taskkill /pid %%i /f >nul 2>&1
)

echo.
echo ✅ 服务已停止
echo.
pause
