# -*- coding: utf-8 -*-
"""
Test Encoding Compatibility
测试编码兼容性
"""
import sys
import os

def test_encoding():
    """Test encoding compatibility"""
    print("=" * 50)
    print("Encoding Compatibility Test")
    print("编码兼容性测试")
    print("=" * 50)
    
    # Test system encoding
    print(f"System encoding: {sys.getdefaultencoding()}")
    print(f"File system encoding: {sys.getfilesystemencoding()}")
    
    # Test Chinese characters
    test_strings = [
        "企业级文件共享系统",
        "数据库初始化",
        "用户认证",
        "文件管理",
        "权限控制"
    ]
    
    print("\nTesting Chinese characters:")
    print("测试中文字符:")
    for i, text in enumerate(test_strings, 1):
        try:
            print(f"{i}. {text} - OK")
        except UnicodeEncodeError as e:
            print(f"{i}. Error encoding: {e}")
    
    # Test file operations
    print("\nTesting file operations:")
    print("测试文件操作:")
    
    try:
        test_file = "test_中文.txt"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("测试中文内容\nTest Chinese content")
        
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"File content: {content}")
        
        os.remove(test_file)
        print("File operations: OK")
        
    except Exception as e:
        print(f"File operations error: {e}")
    
    print("\n" + "=" * 50)
    print("Test completed!")
    print("测试完成!")
    print("=" * 50)

if __name__ == "__main__":
    test_encoding()
