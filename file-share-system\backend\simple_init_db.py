# -*- coding: utf-8 -*-
"""
Simple Database Initialization Script
简化的数据库初始化脚本
"""
import os
import sys

# Set UTF-8 encoding for Windows
if sys.platform.startswith('win'):
    import locale
    try:
        locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'C.UTF-8')
        except:
            pass

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """Main function"""
    print("=" * 50)
    print("Database Initialization")
    print("数据库初始化")
    print("=" * 50)
    
    try:
        from app.database import init_database, get_db
        from app.models.user import User, UserRole
        from passlib.context import CryptContext
        
        print("Creating database tables...")
        print("创建数据库表...")
        
        # Initialize database
        init_database()
        print("OK: Database tables created")
        print("完成: 数据库表创建成功")
        
        # Create default admin user
        print("Creating default admin user...")
        print("创建默认管理员用户...")
        
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        db = next(get_db())
        try:
            # Check if admin exists
            admin_exists = db.query(User).filter(User.role == UserRole.ADMIN).first()
            if admin_exists:
                print("Admin user already exists, skipping...")
                print("管理员用户已存在，跳过创建...")
            else:
                # Create admin user
                admin_user = User(
                    username="admin",
                    email="<EMAIL>",
                    password_hash=pwd_context.hash("admin123"),
                    full_name="System Administrator",
                    role=UserRole.ADMIN,
                    is_active=True,
                    is_verified=True,
                    internal_access=True,
                    external_access=True
                )
                
                db.add(admin_user)
                db.commit()
                
                print("OK: Default admin user created")
                print("完成: 默认管理员用户创建成功")
                print("Username/用户名: admin")
                print("Password/密码: admin123")
                
        except Exception as e:
            db.rollback()
            print(f"Error creating admin user: {e}")
            print(f"创建管理员用户失败: {e}")
            raise
        finally:
            db.close()
        
        print("=" * 50)
        print("Database initialization completed!")
        print("数据库初始化完成!")
        print("You can now start the server with: python simple_run.py")
        print("现在可以启动服务器: python simple_run.py")
        print("=" * 50)
        
    except ImportError as e:
        print(f"Import Error: {e}")
        print("Please install required packages:")
        print("请安装必要的依赖包:")
        print("pip install fastapi uvicorn sqlalchemy pymysql redis python-jose passlib loguru")
        sys.exit(1)
    except Exception as e:
        print(f"Error initializing database: {e}")
        print(f"数据库初始化错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
