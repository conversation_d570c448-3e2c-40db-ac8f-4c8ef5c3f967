"""
管理功能路由
"""
from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session

from app.database import get_db
from app.middleware.auth import require_admin

router = APIRouter()


@router.get("/dashboard")
async def admin_dashboard(
    request: Request,
    db: Session = Depends(get_db),
    current_user = Depends(require_admin)
):
    """管理员仪表板"""
    # TODO: 实现管理员仪表板逻辑
    return {"message": "管理员仪表板功能待实现"}


@router.get("/system-stats")
async def system_stats(
    request: Request,
    db: Session = Depends(get_db),
    current_user = Depends(require_admin)
):
    """系统统计信息"""
    # TODO: 实现系统统计逻辑
    return {"message": "系统统计功能待实现"}
