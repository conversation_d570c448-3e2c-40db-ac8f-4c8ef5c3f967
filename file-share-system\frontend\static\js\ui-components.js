// UI组件库

// 创建模态框
function createModal(title, content, buttons = []) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">${title}</h2>
                <button class="modal-close" onclick="closeModal()">×</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
            <div class="modal-footer">
                ${buttons.map(btn => `
                    <button class="btn ${btn.class}" onclick="${btn.onclick ? btn.onclick.toString().replace('function ', '').replace('()', '()') : 'closeModal()'}">${btn.text}</button>
                `).join('')}
            </div>
        </div>
    `;
    
    return modal;
}

// 显示模态框
function showModal(modal) {
    const container = document.getElementById('modal-container');
    container.innerHTML = '';
    container.appendChild(modal);
    
    // 显示动画
    setTimeout(() => modal.classList.add('show'), 10);
    
    // ESC键关闭
    const escHandler = (e) => {
        if (e.key === 'Escape') {
            closeModal();
            document.removeEventListener('keydown', escHandler);
        }
    };
    document.addEventListener('keydown', escHandler);
    
    // 点击背景关闭
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal();
        }
    });
}

// 关闭模态框
function closeModal() {
    const modal = document.querySelector('.modal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            const container = document.getElementById('modal-container');
            container.innerHTML = '';
        }, 300);
    }
}

// 创建确认对话框
function showConfirmDialog(title, message, onConfirm, onCancel = null) {
    const modal = createModal(title, `
        <p>${message}</p>
    `, [
        {
            text: '取消',
            class: 'btn-secondary',
            onclick: () => {
                closeModal();
                if (onCancel) onCancel();
            }
        },
        {
            text: '确认',
            class: 'btn-primary',
            onclick: () => {
                closeModal();
                if (onConfirm) onConfirm();
            }
        }
    ]);
    
    showModal(modal);
}

// 创建输入对话框
function showInputDialog(title, placeholder, defaultValue = '', onConfirm = null) {
    const modal = createModal(title, `
        <div class="form-group">
            <input type="text" id="input-dialog-value" class="form-input" placeholder="${placeholder}" value="${defaultValue}">
        </div>
    `, [
        {
            text: '取消',
            class: 'btn-secondary',
            onclick: () => closeModal()
        },
        {
            text: '确认',
            class: 'btn-primary',
            onclick: () => {
                const value = document.getElementById('input-dialog-value').value.trim();
                closeModal();
                if (onConfirm && value) onConfirm(value);
            }
        }
    ]);
    
    showModal(modal);
    
    // 焦点到输入框
    setTimeout(() => {
        const input = document.getElementById('input-dialog-value');
        input.focus();
        input.select();
    }, 100);
    
    // 回车确认
    document.getElementById('input-dialog-value').addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            const value = e.target.value.trim();
            closeModal();
            if (onConfirm && value) onConfirm(value);
        }
    });
}

// 创建上下文菜单
function showContextMenu(e, items) {
    e.preventDefault();
    
    // 移除现有的上下文菜单
    const existingMenu = document.querySelector('.context-menu');
    if (existingMenu) {
        existingMenu.remove();
    }
    
    const menu = document.createElement('div');
    menu.className = 'context-menu';
    menu.innerHTML = items.map(item => {
        if (item.separator) {
            return '<div class="context-menu-separator"></div>';
        }
        return `
            <div class="context-menu-item ${item.class || ''}" onclick="${item.onclick}">
                <span class="context-menu-icon">${item.icon || ''}</span>
                <span class="context-menu-text">${item.text}</span>
            </div>
        `;
    }).join('');
    
    document.body.appendChild(menu);
    
    // 定位菜单
    const rect = menu.getBoundingClientRect();
    const x = Math.min(e.clientX, window.innerWidth - rect.width - 10);
    const y = Math.min(e.clientY, window.innerHeight - rect.height - 10);
    
    menu.style.left = x + 'px';
    menu.style.top = y + 'px';
    
    // 显示动画
    setTimeout(() => menu.classList.add('show'), 10);
    
    // 点击其他地方关闭
    const closeHandler = (event) => {
        if (!menu.contains(event.target)) {
            menu.classList.remove('show');
            setTimeout(() => menu.remove(), 200);
            document.removeEventListener('click', closeHandler);
        }
    };
    setTimeout(() => document.addEventListener('click', closeHandler), 100);
}

// 创建加载指示器
function createLoadingIndicator(text = '加载中...') {
    const loading = document.createElement('div');
    loading.className = 'loading-indicator';
    loading.innerHTML = `
        <div class="loading-spinner"></div>
        <div class="loading-text">${text}</div>
    `;
    return loading;
}

// 显示全屏加载
function showFullScreenLoading(text = '加载中...') {
    const overlay = document.createElement('div');
    overlay.className = 'loading-overlay';
    overlay.innerHTML = `
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">${text}</div>
        </div>
    `;
    
    document.body.appendChild(overlay);
    setTimeout(() => overlay.classList.add('show'), 10);
    
    return overlay;
}

// 隐藏全屏加载
function hideFullScreenLoading() {
    const overlay = document.querySelector('.loading-overlay');
    if (overlay) {
        overlay.classList.remove('show');
        setTimeout(() => overlay.remove(), 300);
    }
}

// 创建进度条
function createProgressBar(value = 0, max = 100) {
    const progress = document.createElement('div');
    progress.className = 'progress-bar';
    progress.innerHTML = `
        <div class="progress-fill" style="width: ${(value / max) * 100}%"></div>
    `;
    
    // 更新进度的方法
    progress.updateProgress = (newValue) => {
        const fill = progress.querySelector('.progress-fill');
        fill.style.width = `${(newValue / max) * 100}%`;
    };
    
    return progress;
}

// 创建标签页
function createTabs(tabs, activeIndex = 0) {
    const tabContainer = document.createElement('div');
    tabContainer.className = 'tab-container';
    
    const tabHeaders = document.createElement('div');
    tabHeaders.className = 'tab-headers';
    
    const tabContents = document.createElement('div');
    tabContents.className = 'tab-contents';
    
    tabs.forEach((tab, index) => {
        // 创建标签头
        const header = document.createElement('button');
        header.className = `tab-header ${index === activeIndex ? 'active' : ''}`;
        header.textContent = tab.title;
        header.onclick = () => switchTab(index);
        tabHeaders.appendChild(header);
        
        // 创建标签内容
        const content = document.createElement('div');
        content.className = `tab-content ${index === activeIndex ? 'active' : ''}`;
        content.innerHTML = tab.content;
        tabContents.appendChild(content);
    });
    
    tabContainer.appendChild(tabHeaders);
    tabContainer.appendChild(tabContents);
    
    // 切换标签的方法
    function switchTab(index) {
        // 更新标签头
        tabHeaders.querySelectorAll('.tab-header').forEach((header, i) => {
            header.classList.toggle('active', i === index);
        });
        
        // 更新标签内容
        tabContents.querySelectorAll('.tab-content').forEach((content, i) => {
            content.classList.toggle('active', i === index);
        });
        
        // 触发回调
        if (tabs[index].onActivate) {
            tabs[index].onActivate();
        }
    }
    
    tabContainer.switchTab = switchTab;
    
    return tabContainer;
}

// 创建下拉菜单
function createDropdown(trigger, items) {
    const dropdown = document.createElement('div');
    dropdown.className = 'dropdown';
    
    const menu = document.createElement('div');
    menu.className = 'dropdown-menu';
    menu.innerHTML = items.map(item => `
        <div class="dropdown-item" onclick="${item.onclick}">
            ${item.icon ? `<span class="dropdown-icon">${item.icon}</span>` : ''}
            <span class="dropdown-text">${item.text}</span>
        </div>
    `).join('');
    
    dropdown.appendChild(menu);
    
    // 切换显示/隐藏
    trigger.addEventListener('click', (e) => {
        e.stopPropagation();
        const isVisible = menu.classList.contains('show');
        
        // 隐藏所有其他下拉菜单
        document.querySelectorAll('.dropdown-menu.show').forEach(m => {
            m.classList.remove('show');
        });
        
        if (!isVisible) {
            menu.classList.add('show');
            
            // 定位菜单
            const triggerRect = trigger.getBoundingClientRect();
            const menuRect = menu.getBoundingClientRect();
            
            let top = triggerRect.bottom + 5;
            let left = triggerRect.left;
            
            // 防止菜单超出屏幕
            if (left + menuRect.width > window.innerWidth) {
                left = triggerRect.right - menuRect.width;
            }
            
            if (top + menuRect.height > window.innerHeight) {
                top = triggerRect.top - menuRect.height - 5;
            }
            
            menu.style.top = top + 'px';
            menu.style.left = left + 'px';
        }
    });
    
    // 点击其他地方关闭
    document.addEventListener('click', () => {
        menu.classList.remove('show');
    });
    
    return dropdown;
}

// 创建工具提示
function createTooltip(element, text, position = 'top') {
    element.setAttribute('data-tooltip', text);
    element.classList.add('tooltip');
    
    // 可以添加更多位置选项的逻辑
    if (position !== 'top') {
        element.classList.add(`tooltip-${position}`);
    }
}

// 创建面包屑导航
function createBreadcrumb(items) {
    const breadcrumb = document.createElement('div');
    breadcrumb.className = 'breadcrumb';
    
    breadcrumb.innerHTML = items.map((item, index) => {
        const isLast = index === items.length - 1;
        return `
            <span class="breadcrumb-item ${isLast ? 'active' : ''}" ${!isLast ? `onclick="${item.onclick}"` : ''}>
                ${item.text}
            </span>
            ${!isLast ? '<span class="breadcrumb-separator">></span>' : ''}
        `;
    }).join('');
    
    return breadcrumb;
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        createModal,
        showModal,
        closeModal,
        showConfirmDialog,
        showInputDialog,
        showContextMenu,
        createLoadingIndicator,
        showFullScreenLoading,
        hideFullScreenLoading,
        createProgressBar,
        createTabs,
        createDropdown,
        createTooltip,
        createBreadcrumb
    };
}
