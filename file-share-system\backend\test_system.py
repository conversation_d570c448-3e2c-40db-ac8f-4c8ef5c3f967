"""
系统测试脚本
"""
import os
import sys
import requests
import time
from loguru import logger

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config import get_settings
from app.database import check_database_health, check_redis_health

settings = get_settings()
BASE_URL = f"http://{settings.HOST}:{settings.PORT}"


def test_database_connection():
    """测试数据库连接"""
    logger.info("🔍 测试数据库连接...")
    try:
        if check_database_health():
            logger.info("✅ 数据库连接正常")
            return True
        else:
            logger.error("❌ 数据库连接失败")
            return False
    except Exception as e:
        logger.error(f"❌ 数据库连接测试失败: {e}")
        return False


async def test_redis_connection():
    """测试Redis连接"""
    logger.info("🔍 测试Redis连接...")
    try:
        if await check_redis_health():
            logger.info("✅ Redis连接正常")
            return True
        else:
            logger.error("❌ Redis连接失败")
            return False
    except Exception as e:
        logger.error(f"❌ Redis连接测试失败: {e}")
        return False


def test_api_endpoints():
    """测试API端点"""
    logger.info("🔍 测试API端点...")
    
    endpoints = [
        {"url": "/", "name": "根路径"},
        {"url": "/health", "name": "健康检查"},
        {"url": "/docs", "name": "API文档"},
    ]
    
    success_count = 0
    
    for endpoint in endpoints:
        try:
            url = BASE_URL + endpoint["url"]
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                logger.info(f"✅ {endpoint['name']}: {response.status_code}")
                success_count += 1
            else:
                logger.warning(f"⚠️  {endpoint['name']}: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            logger.error(f"❌ {endpoint['name']}: 连接失败")
        except requests.exceptions.Timeout:
            logger.error(f"❌ {endpoint['name']}: 请求超时")
        except Exception as e:
            logger.error(f"❌ {endpoint['name']}: {e}")
    
    return success_count == len(endpoints)


def test_authentication():
    """测试认证功能"""
    logger.info("🔍 测试认证功能...")
    
    try:
        # 测试登录
        login_url = BASE_URL + "/api/auth/login"
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = requests.post(login_url, json=login_data, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            if "access_token" in data:
                logger.info("✅ 认证功能正常")
                return True
            else:
                logger.error("❌ 认证响应格式错误")
                return False
        else:
            logger.error(f"❌ 认证失败: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 认证测试失败: {e}")
        return False


def test_file_operations():
    """测试文件操作"""
    logger.info("🔍 测试文件操作...")
    
    try:
        # 首先登录获取token
        login_url = BASE_URL + "/api/auth/login"
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = requests.post(login_url, json=login_data, timeout=5)
        if response.status_code != 200:
            logger.error("❌ 无法获取认证token")
            return False
        
        token = response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # 测试文件列表
        files_url = BASE_URL + "/api/files/"
        response = requests.get(files_url, headers=headers, timeout=5)
        
        if response.status_code == 200:
            logger.info("✅ 文件操作功能正常")
            return True
        else:
            logger.error(f"❌ 文件操作失败: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 文件操作测试失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("🧪 开始系统测试...")
    logger.info("=" * 50)
    
    test_results = []
    
    # 测试数据库连接
    test_results.append(test_database_connection())
    
    # 测试Redis连接
    import asyncio
    test_results.append(asyncio.run(test_redis_connection()))
    
    # 等待一下，确保服务启动完成
    logger.info("⏳ 等待服务启动...")
    time.sleep(2)
    
    # 测试API端点
    test_results.append(test_api_endpoints())
    
    # 测试认证功能
    test_results.append(test_authentication())
    
    # 测试文件操作
    test_results.append(test_file_operations())
    
    # 汇总结果
    logger.info("=" * 50)
    passed = sum(test_results)
    total = len(test_results)
    
    if passed == total:
        logger.info(f"🎉 所有测试通过! ({passed}/{total})")
        logger.info("✅ 系统运行正常，可以开始使用")
    else:
        logger.warning(f"⚠️  部分测试失败 ({passed}/{total})")
        logger.info("🔧 请检查失败的组件并重新测试")
    
    logger.info("=" * 50)
    logger.info("📍 访问地址:")
    logger.info(f"   前端页面: {BASE_URL}/static/index.html")
    logger.info(f"   API文档:  {BASE_URL}/docs")
    logger.info(f"   健康检查: {BASE_URL}/health")
    logger.info("👤 默认管理员: admin / admin123")


if __name__ == "__main__":
    main()
