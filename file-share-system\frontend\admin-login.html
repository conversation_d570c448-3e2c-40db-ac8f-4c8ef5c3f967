<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 企业级文件共享系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            margin: 20px;
        }

        .login-header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .admin-icon {
            font-size: 3em;
            margin-bottom: 10px;
            display: block;
        }

        .login-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .login-subtitle {
            opacity: 0.9;
            font-size: 0.9em;
        }

        .login-form {
            padding: 40px 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s;
            background: #f8f9fa;
        }

        .form-input:focus {
            outline: none;
            border-color: #ff6b6b;
            background: white;
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
        }

        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 25px;
        }

        .form-checkbox input {
            width: 18px;
            height: 18px;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-links {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }

        .login-link {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s;
        }

        .login-link:hover {
            color: #5a6fd8;
            text-decoration: underline;
        }

        .security-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #856404;
        }

        .security-icon {
            color: #f39c12;
            margin-right: 5px;
        }

        .error-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }

        .success-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 10px;
            }
            
            .login-header {
                padding: 25px 20px;
            }
            
            .login-form {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <span class="admin-icon">👨‍💼</span>
            <h1 class="login-title">管理员登录</h1>
            <p class="login-subtitle">企业级文件共享系统</p>
        </div>

        <div class="login-form">
            <div class="security-notice">
                <span class="security-icon">🔒</span>
                管理员账户具有系统最高权限，请确保在安全环境下登录。
            </div>

            <div id="error-message" class="error-message"></div>
            <div id="success-message" class="success-message"></div>

            <form id="admin-login-form">
                <div class="form-group">
                    <label class="form-label">管理员账户</label>
                    <input type="text" id="admin-username" class="form-input" placeholder="请输入管理员用户名" required>
                </div>

                <div class="form-group">
                    <label class="form-label">管理员密码</label>
                    <input type="password" id="admin-password" class="form-input" placeholder="请输入管理员密码" required>
                </div>

                <div class="form-checkbox">
                    <input type="checkbox" id="admin-remember">
                    <label for="admin-remember">记住登录状态（7天）</label>
                </div>

                <button type="submit" id="admin-login-btn" class="login-btn">
                    登录管理后台
                </button>
            </form>

            <div class="login-links">
                <a href="index.html" class="login-link">← 返回用户登录</a>
            </div>
        </div>
    </div>

    <script>
        // 管理员登录处理
        document.getElementById('admin-login-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('admin-username').value.trim();
            const password = document.getElementById('admin-password').value;
            const remember = document.getElementById('admin-remember').checked;
            
            if (!username || !password) {
                showError('请输入用户名和密码');
                return;
            }
            
            const loginBtn = document.getElementById('admin-login-btn');
            const originalText = loginBtn.textContent;
            
            try {
                loginBtn.textContent = '登录中...';
                loginBtn.disabled = true;
                hideMessages();
                
                // 调用管理员登录API
                const response = await fetch('http://localhost:8000/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username,
                        password,
                        remember_me: remember
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // 保存管理员令牌
                    localStorage.setItem('admin_token', data.access_token);
                    localStorage.setItem('user_role', 'admin');
                    
                    if (data.refresh_token) {
                        localStorage.setItem('admin_refresh_token', data.refresh_token);
                    }
                    
                    showSuccess('登录成功，正在跳转...');
                    
                    // 跳转到管理员界面
                    setTimeout(() => {
                        window.location.href = 'admin-dashboard.html';
                    }, 1000);
                    
                } else {
                    showError(data.message || '登录失败，请检查用户名和密码');
                }
                
            } catch (error) {
                console.error('登录错误:', error);
                showError('网络连接失败，请检查后端服务是否启动');
            } finally {
                loginBtn.textContent = originalText;
                loginBtn.disabled = false;
            }
        });
        
        // 显示错误消息
        function showError(message) {
            const errorEl = document.getElementById('error-message');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
            
            setTimeout(() => {
                errorEl.style.display = 'none';
            }, 5000);
        }
        
        // 显示成功消息
        function showSuccess(message) {
            const successEl = document.getElementById('success-message');
            successEl.textContent = message;
            successEl.style.display = 'block';
        }
        
        // 隐藏所有消息
        function hideMessages() {
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-message').style.display = 'none';
        }
        
        // 检查是否已经登录
        window.addEventListener('load', () => {
            const adminToken = localStorage.getItem('admin_token');
            const userRole = localStorage.getItem('user_role');
            
            if (adminToken && userRole === 'admin') {
                // 验证令牌有效性
                fetch('http://localhost:8000/api/admin/verify', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`
                    }
                }).then(response => {
                    if (response.ok) {
                        window.location.href = 'admin-dashboard.html';
                    }
                }).catch(() => {
                    // 令牌无效，清除本地存储
                    localStorage.removeItem('admin_token');
                    localStorage.removeItem('admin_refresh_token');
                    localStorage.removeItem('user_role');
                });
            }
        });
    </script>
</body>
</html>
