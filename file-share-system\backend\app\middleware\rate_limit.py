"""
限流中间件
"""
import time
from typing import Optional
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger

from app.config import get_settings
from app.database import cache_manager

settings = get_settings()


class RateLimitMiddleware(BaseHTTPMiddleware):
    """限流中间件"""
    
    def __init__(self, app, requests_per_minute: int = None, window_seconds: int = None):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute or settings.RATE_LIMIT_REQUESTS
        self.window_seconds = window_seconds or settings.RATE_LIMIT_WINDOW_SECONDS
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        # 获取客户端标识
        client_id = self.get_client_identifier(request)
        
        # 检查限流
        try:
            if not await self.check_rate_limit(client_id, request.url.path):
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={
                        "error": True,
                        "message": "请求过于频繁，请稍后再试",
                        "status_code": 429,
                        "retry_after": self.window_seconds
                    },
                    headers={"Retry-After": str(self.window_seconds)}
                )
        except Exception as e:
            logger.error(f"限流检查失败: {e}")
            # 限流检查失败时允许请求通过，避免影响正常服务
        
        # 继续处理请求
        response = await call_next(request)
        
        # 添加限流相关头部
        remaining = await self.get_remaining_requests(client_id)
        response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(time.time()) + self.window_seconds)
        
        return response
    
    def get_client_identifier(self, request: Request) -> str:
        """获取客户端标识"""
        # 优先使用用户ID
        user_id = getattr(request.state, "user_id", None)
        if user_id:
            return f"user:{user_id}"
        
        # 使用IP地址
        client_ip = self.get_client_ip(request)
        return f"ip:{client_ip}"
    
    def get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 返回直连IP
        return request.client.host if request.client else "unknown"
    
    async def check_rate_limit(self, client_id: str, path: str) -> bool:
        """检查限流"""
        # 获取当前时间窗口
        current_window = int(time.time()) // self.window_seconds
        
        # 构建缓存键
        cache_key = f"rate_limit:{client_id}:{current_window}"
        
        # 获取当前请求数
        try:
            current_requests = await cache_manager.get(cache_key)
            current_requests = int(current_requests) if current_requests else 0
        except (ValueError, TypeError):
            current_requests = 0
        
        # 检查是否超过限制
        if current_requests >= self.requests_per_minute:
            logger.warning(f"限流触发: {client_id} - {current_requests}/{self.requests_per_minute} - {path}")
            return False
        
        # 增加请求计数
        try:
            await cache_manager.increment(cache_key)
            await cache_manager.expire(cache_key, self.window_seconds)
        except Exception as e:
            logger.error(f"更新限流计数失败: {e}")
        
        return True
    
    async def get_remaining_requests(self, client_id: str) -> int:
        """获取剩余请求数"""
        try:
            current_window = int(time.time()) // self.window_seconds
            cache_key = f"rate_limit:{client_id}:{current_window}"
            
            current_requests = await cache_manager.get(cache_key)
            current_requests = int(current_requests) if current_requests else 0
            
            return max(0, self.requests_per_minute - current_requests)
        except Exception:
            return self.requests_per_minute
    
    def get_rate_limit_for_path(self, path: str) -> tuple[int, int]:
        """根据路径获取特定的限流配置"""
        # 下载接口更严格的限流
        if "/download" in path:
            return 10, 60  # 每分钟10次
        
        # 搜索接口限流
        elif "/search" in path:
            return 30, 60  # 每分钟30次
        
        # 上传接口限流
        elif "/upload" in path:
            return 5, 60   # 每分钟5次
        
        # 登录接口限流
        elif "/auth/login" in path:
            return 5, 300  # 5分钟内5次
        
        # 默认限流
        else:
            return self.requests_per_minute, self.window_seconds


class DownloadRateLimitMiddleware(BaseHTTPMiddleware):
    """下载限流中间件"""
    
    def __init__(self, app):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        # 只对下载请求进行限流
        if "/download" not in request.url.path:
            return await call_next(request)
        
        # 获取用户信息
        user_id = getattr(request.state, "user_id", None)
        if not user_id:
            return await call_next(request)
        
        # 检查下载限制
        try:
            if not await self.check_download_limit(user_id):
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={
                        "error": True,
                        "message": "下载次数已达上限，请稍后再试",
                        "status_code": 429
                    }
                )
        except Exception as e:
            logger.error(f"下载限流检查失败: {e}")
        
        # 继续处理请求
        response = await call_next(request)
        
        # 如果下载成功，增加下载计数
        if response.status_code == 200:
            try:
                await self.increment_download_count(user_id)
            except Exception as e:
                logger.error(f"增加下载计数失败: {e}")
        
        return response
    
    async def check_download_limit(self, user_id: int) -> bool:
        """检查下载限制"""
        # 获取用户今日下载次数
        today = time.strftime("%Y-%m-%d")
        cache_key = f"download_count:{user_id}:{today}"
        
        try:
            download_count = await cache_manager.get(cache_key)
            download_count = int(download_count) if download_count else 0
            
            # 这里应该从用户配置中获取限制，简化处理使用默认值
            max_downloads = 100  # 每日最大下载次数
            
            return download_count < max_downloads
        except Exception:
            return True
    
    async def increment_download_count(self, user_id: int):
        """增加下载计数"""
        today = time.strftime("%Y-%m-%d")
        cache_key = f"download_count:{user_id}:{today}"
        
        try:
            await cache_manager.increment(cache_key)
            # 设置过期时间为明天凌晨
            import datetime
            tomorrow = datetime.datetime.now().replace(
                hour=0, minute=0, second=0, microsecond=0
            ) + datetime.timedelta(days=1)
            expire_seconds = int((tomorrow - datetime.datetime.now()).total_seconds())
            await cache_manager.expire(cache_key, expire_seconds)
        except Exception as e:
            logger.error(f"更新下载计数失败: {e}")


class SecurityMiddleware(BaseHTTPMiddleware):
    """安全中间件"""
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        # 检查可疑行为
        if await self.detect_suspicious_activity(request):
            logger.warning(f"检测到可疑活动: {request.client.host} - {request.url.path}")
            
            # 记录安全告警
            await self.log_security_alert(request)
            
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content={
                    "error": True,
                    "message": "检测到可疑活动，访问被拒绝",
                    "status_code": 403
                }
            )
        
        # 继续处理请求
        response = await call_next(request)
        
        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        return response
    
    async def detect_suspicious_activity(self, request: Request) -> bool:
        """检测可疑活动"""
        # 检查SQL注入尝试
        if self.check_sql_injection(request):
            return True
        
        # 检查XSS尝试
        if self.check_xss_attempt(request):
            return True
        
        # 检查路径遍历尝试
        if self.check_path_traversal(request):
            return True
        
        return False
    
    def check_sql_injection(self, request: Request) -> bool:
        """检查SQL注入"""
        sql_patterns = [
            "union select", "drop table", "insert into", "delete from",
            "update set", "exec(", "execute(", "sp_", "xp_"
        ]
        
        query_string = str(request.url.query).lower()
        path = request.url.path.lower()
        
        for pattern in sql_patterns:
            if pattern in query_string or pattern in path:
                return True
        
        return False
    
    def check_xss_attempt(self, request: Request) -> bool:
        """检查XSS尝试"""
        xss_patterns = [
            "<script", "javascript:", "onload=", "onerror=",
            "alert(", "document.cookie", "eval("
        ]
        
        query_string = str(request.url.query).lower()
        path = request.url.path.lower()
        
        for pattern in xss_patterns:
            if pattern in query_string or pattern in path:
                return True
        
        return False
    
    def check_path_traversal(self, request: Request) -> bool:
        """检查路径遍历"""
        path = request.url.path
        
        if "../" in path or "..%2f" in path.lower() or "..%5c" in path.lower():
            return True
        
        return False
    
    async def log_security_alert(self, request: Request):
        """记录安全告警"""
        try:
            from app.models.activity_log import SecurityAlert
            
            alert = SecurityAlert(
                alert_type="suspicious_activity",
                severity="medium",
                title="检测到可疑活动",
                description=f"来自 {request.client.host} 的可疑请求: {request.url.path}",
                ip_address=request.client.host,
                details=f'{{"path": "{request.url.path}", "query": "{request.url.query}", "user_agent": "{request.headers.get("User-Agent", "")}"}}',
                status="open"
            )
            
            db = next(get_db())
            try:
                db.add(alert)
                db.commit()
            except Exception as e:
                logger.error(f"保存安全告警失败: {e}")
                db.rollback()
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"记录安全告警失败: {e}")
