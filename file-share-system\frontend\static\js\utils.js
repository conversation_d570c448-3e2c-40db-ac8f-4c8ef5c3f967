// 工具函数库

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    
    // 小于1分钟
    if (diff < 60000) {
        return '刚刚';
    }
    
    // 小于1小时
    if (diff < 3600000) {
        return Math.floor(diff / 60000) + '分钟前';
    }
    
    // 小于1天
    if (diff < 86400000) {
        return Math.floor(diff / 3600000) + '小时前';
    }
    
    // 小于7天
    if (diff < 604800000) {
        return Math.floor(diff / 86400000) + '天前';
    }
    
    // 超过7天显示具体日期
    return date.toLocaleDateString('zh-CN');
}

// 获取文件类型图标
function getFileIcon(filename, isFolder = false) {
    if (isFolder) {
        return '📁';
    }
    
    const ext = filename.split('.').pop().toLowerCase();
    
    const iconMap = {
        // 图片
        'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️', 
        'bmp': '🖼️', 'tiff': '🖼️', 'tif': '🖼️', 'webp': '🖼️',
        'psd': '🎨', 'ai': '🎨', 'eps': '🎨', 'svg': '🎨',
        
        // 文档
        'pdf': '📄', 'doc': '📝', 'docx': '📝', 'txt': '📄',
        'xls': '📊', 'xlsx': '📊', 'ppt': '📽️', 'pptx': '📽️',
        
        // 视频
        'mp4': '🎬', 'avi': '🎬', 'mov': '🎬', 'wmv': '🎬',
        'flv': '🎬', 'mkv': '🎬', 'webm': '🎬',
        
        // 音频
        'mp3': '🎵', 'wav': '🎵', 'flac': '🎵', 'aac': '🎵',
        'ogg': '🎵', 'wma': '🎵',
        
        // 压缩包
        'zip': '📦', 'rar': '📦', '7z': '📦', 'tar': '📦',
        'gz': '📦', 'bz2': '📦',
        
        // 代码
        'js': '💻', 'html': '💻', 'css': '💻', 'py': '💻',
        'java': '💻', 'cpp': '💻', 'c': '💻', 'php': '💻',
        'json': '💻', 'xml': '💻', 'sql': '💻'
    };
    
    return iconMap[ext] || '📄';
}

// 获取文件类型类名
function getFileTypeClass(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    
    const typeMap = {
        'jpg': 'image', 'jpeg': 'image', 'png': 'image', 'gif': 'image',
        'bmp': 'image', 'tiff': 'image', 'tif': 'image', 'webp': 'image',
        'psd': 'image', 'ai': 'image', 'eps': 'image', 'svg': 'image',
        
        'pdf': 'document', 'doc': 'document', 'docx': 'document', 'txt': 'document',
        'xls': 'document', 'xlsx': 'document', 'ppt': 'document', 'pptx': 'document',
        
        'mp4': 'video', 'avi': 'video', 'mov': 'video', 'wmv': 'video',
        'flv': 'video', 'mkv': 'video', 'webm': 'video',
        
        'mp3': 'audio', 'wav': 'audio', 'flac': 'audio', 'aac': 'audio',
        'ogg': 'audio', 'wma': 'audio',
        
        'zip': 'archive', 'rar': 'archive', '7z': 'archive', 'tar': 'archive',
        'gz': 'archive', 'bz2': 'archive',
        
        'js': 'code', 'html': 'code', 'css': 'code', 'py': 'code',
        'java': 'code', 'cpp': 'code', 'c': 'code', 'php': 'code',
        'json': 'code', 'xml': 'code', 'sql': 'code'
    };
    
    return typeMap[ext] || 'document';
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 生成唯一ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 复制到剪贴板
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (err) {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            return true;
        } catch (err) {
            return false;
        } finally {
            document.body.removeChild(textArea);
        }
    }
}

// 下载文件
function downloadFile(url, filename) {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 验证文件类型
function validateFileType(filename, allowedTypes) {
    const ext = filename.split('.').pop().toLowerCase();
    return allowedTypes.includes(ext);
}

// 验证文件大小
function validateFileSize(size, maxSize) {
    return size <= maxSize;
}

// 转义HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 解析查询参数
function parseQueryParams(url = window.location.href) {
    const params = {};
    const urlObj = new URL(url);
    for (const [key, value] of urlObj.searchParams) {
        params[key] = value;
    }
    return params;
}

// 设置查询参数
function setQueryParam(key, value) {
    const url = new URL(window.location);
    url.searchParams.set(key, value);
    window.history.pushState({}, '', url);
}

// 移除查询参数
function removeQueryParam(key) {
    const url = new URL(window.location);
    url.searchParams.delete(key);
    window.history.pushState({}, '', url);
}

// 检查是否为移动设备
function isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// 检查是否支持拖拽
function supportsDragAndDrop() {
    const div = document.createElement('div');
    return (('draggable' in div) || ('ondragstart' in div && 'ondrop' in div)) && 
           'FormData' in window && 'FileReader' in window;
}

// 获取文件扩展名
function getFileExtension(filename) {
    return filename.split('.').pop().toLowerCase();
}

// 获取文件名（不含扩展名）
function getFileNameWithoutExtension(filename) {
    return filename.substring(0, filename.lastIndexOf('.')) || filename;
}

// 格式化数字
function formatNumber(num) {
    return new Intl.NumberFormat('zh-CN').format(num);
}

// 深拷贝对象
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
}

// 等待指定时间
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 重试函数
async function retry(fn, maxAttempts = 3, delay = 1000) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await fn();
        } catch (error) {
            if (attempt === maxAttempts) {
                throw error;
            }
            await sleep(delay * attempt);
        }
    }
}

// 导出所有函数（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        formatFileSize,
        formatDate,
        getFileIcon,
        getFileTypeClass,
        debounce,
        throttle,
        generateId,
        copyToClipboard,
        downloadFile,
        validateFileType,
        validateFileSize,
        escapeHtml,
        parseQueryParams,
        setQueryParam,
        removeQueryParam,
        isMobile,
        supportsDragAndDrop,
        getFileExtension,
        getFileNameWithoutExtension,
        formatNumber,
        deepClone,
        sleep,
        retry
    };
}
