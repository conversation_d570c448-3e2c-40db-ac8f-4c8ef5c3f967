/* 组件样式文件 */

/* 文件项样式 */
.file-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
}

.file-item:hover {
    border-color: #667eea;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.file-item.selected {
    border-color: #667eea;
    background: #f0f4ff;
}

.file-icon {
    font-size: 3em;
    margin-bottom: 10px;
    display: block;
}

.file-thumbnail {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 10px;
}

.file-name {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 5px;
    word-break: break-word;
    line-height: 1.3;
}

.file-info {
    font-size: 12px;
    color: #666;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-size {
    font-weight: 500;
}

.file-date {
    opacity: 0.8;
}

/* 文件操作菜单 */
.file-menu {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0,0,0,0.7);
    border-radius: 4px;
    opacity: 0;
    transition: opacity 0.3s;
}

.file-item:hover .file-menu {
    opacity: 1;
}

.file-menu-btn {
    background: none;
    border: none;
    color: white;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 12px;
}

/* 文件类型图标 */
.file-icon.image { color: #ff6b6b; }
.file-icon.document { color: #4ecdc4; }
.file-icon.video { color: #45b7d1; }
.file-icon.audio { color: #96ceb4; }
.file-icon.archive { color: #feca57; }
.file-icon.code { color: #ff9ff3; }
.file-icon.folder { color: #54a0ff; }

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.modal-title {
    font-size: 1.5em;
    font-weight: 600;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5em;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s;
}

.modal-close:hover {
    background: #f0f0f0;
    color: #333;
}

.modal-body {
    margin-bottom: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding-top: 15px;
    border-top: 1px solid #e0e0e0;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

/* 上传区域样式 */
.upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s;
    cursor: pointer;
}

.upload-area.dragover {
    border-color: #667eea;
    background: #f0f4ff;
}

.upload-icon {
    font-size: 3em;
    color: #667eea;
    margin-bottom: 15px;
}

.upload-text {
    font-size: 16px;
    color: #333;
    margin-bottom: 5px;
}

.upload-hint {
    font-size: 14px;
    color: #666;
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.3s;
    width: 0%;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    padding: 15px 20px;
    max-width: 350px;
    z-index: 3000;
    transform: translateX(400px);
    transition: transform 0.3s;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid #28a745;
}

.notification.error {
    border-left: 4px solid #dc3545;
}

.notification.warning {
    border-left: 4px solid #ffc107;
}

.notification.info {
    border-left: 4px solid #17a2b8;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.notification-title {
    font-weight: 600;
    color: #333;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
    padding: 0;
}

.notification-message {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

/* 上下文菜单样式 */
.context-menu {
    position: fixed;
    background: white;
    border-radius: 6px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    border: 1px solid #e0e0e0;
    min-width: 150px;
    z-index: 2500;
    opacity: 0;
    visibility: hidden;
    transform: scale(0.9);
    transition: all 0.2s;
}

.context-menu.show {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

.context-menu-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    cursor: pointer;
    transition: background 0.3s;
    font-size: 14px;
    border-bottom: 1px solid #f0f0f0;
}

.context-menu-item:last-child {
    border-bottom: none;
}

.context-menu-item:hover {
    background: #f8f9fa;
}

.context-menu-item.danger:hover {
    background: #fff5f5;
    color: #dc3545;
}

.context-menu-icon {
    font-size: 16px;
    width: 16px;
    text-align: center;
}

/* 搜索结果样式 */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1500;
    display: none;
}

.search-result-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    cursor: pointer;
    transition: background 0.3s;
    border-bottom: 1px solid #f0f0f0;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item:hover {
    background: #f8f9fa;
}

.search-result-icon {
    font-size: 20px;
    width: 20px;
    text-align: center;
}

.search-result-info {
    flex: 1;
}

.search-result-name {
    font-weight: 500;
    margin-bottom: 2px;
}

.search-result-path {
    font-size: 12px;
    color: #666;
}

/* 标签样式 */
.tag {
    display: inline-block;
    background: #f0f0f0;
    color: #666;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    margin: 2px;
}

.tag.sensitive {
    background: #fff5f5;
    color: #dc3545;
}

.tag.encrypted {
    background: #fff3cd;
    color: #856404;
}

/* 工具提示样式 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
    z-index: 4000;
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .file-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .file-item {
        padding: 10px;
    }
    
    .file-icon {
        font-size: 2em;
    }
    
    .modal-content {
        margin: 20px;
        padding: 20px;
    }
    
    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }
    
    .notification.show {
        transform: translateY(0);
    }
}
