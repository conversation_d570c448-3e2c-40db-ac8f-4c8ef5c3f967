// API接口管理

class ApiClient {
    constructor(baseURL = 'http://localhost:8000') {
        this.baseURL = baseURL;
        this.token = localStorage.getItem('access_token');
    }

    // 设置认证令牌
    setToken(token) {
        this.token = token;
        if (token) {
            localStorage.setItem('access_token', token);
        } else {
            localStorage.removeItem('access_token');
        }
    }

    // 获取请求头
    getHeaders(contentType = 'application/json') {
        const headers = {
            'Content-Type': contentType
        };
        
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }
        
        return headers;
    }

    // 通用请求方法
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: this.getHeaders(options.contentType),
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            // 处理401未授权错误
            if (response.status === 401) {
                this.setToken(null);
                window.location.href = '/login.html';
                return;
            }

            // 处理其他HTTP错误
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP ${response.status}`);
            }

            // 处理空响应
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return response;
            }
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    // GET请求
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        
        return this.request(url, {
            method: 'GET'
        });
    }

    // POST请求
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT请求
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE请求
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }

    // 文件上传
    async upload(endpoint, formData) {
        return this.request(endpoint, {
            method: 'POST',
            body: formData,
            contentType: null // 让浏览器自动设置Content-Type
        });
    }

    // 认证相关API
    async login(username, password, rememberMe = false) {
        return this.post('/api/auth/login', {
            username,
            password,
            remember_me: rememberMe
        });
    }

    async logout() {
        return this.post('/api/auth/logout');
    }

    async register(userData) {
        return this.post('/api/auth/register', userData);
    }

    async getCurrentUser() {
        return this.get('/api/auth/me');
    }

    async refreshToken(refreshToken) {
        return this.post('/api/auth/refresh', { refresh_token: refreshToken });
    }

    // 文件管理API
    async getFiles(folderId = null, page = 1, size = 20) {
        return this.get('/api/files/', {
            folder_id: folderId,
            page,
            size
        });
    }

    async uploadFile(file, folderId = null, onProgress = null) {
        const formData = new FormData();
        formData.append('file', file);
        if (folderId) {
            formData.append('folder_id', folderId);
        }

        // 如果需要进度回调，使用XMLHttpRequest
        if (onProgress) {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        onProgress(percentComplete);
                    }
                });

                xhr.addEventListener('load', () => {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            resolve(xhr.responseText);
                        }
                    } else {
                        reject(new Error(`HTTP ${xhr.status}`));
                    }
                });

                xhr.addEventListener('error', () => {
                    reject(new Error('Upload failed'));
                });

                xhr.open('POST', `${this.baseURL}/api/files/upload`);
                if (this.token) {
                    xhr.setRequestHeader('Authorization', `Bearer ${this.token}`);
                }
                xhr.send(formData);
            });
        }

        return this.upload('/api/files/upload', formData);
    }

    async downloadFile(fileId) {
        const response = await this.request(`/api/files/${fileId}/download`, {
            method: 'GET'
        });
        return response;
    }

    async deleteFile(fileId) {
        return this.delete(`/api/files/${fileId}`);
    }

    async renameFile(fileId, newName) {
        return this.put(`/api/files/${fileId}`, { filename: newName });
    }

    async moveFile(fileId, targetFolderId) {
        return this.put(`/api/files/${fileId}`, { folder_id: targetFolderId });
    }

    // 文件夹管理API
    async createFolder(name, parentId = null) {
        return this.post('/api/folders/', {
            name,
            parent_id: parentId
        });
    }

    async getFolders(parentId = null) {
        return this.get('/api/folders/', { parent_id: parentId });
    }

    async deleteFolder(folderId) {
        return this.delete(`/api/folders/${folderId}`);
    }

    async renameFolder(folderId, newName) {
        return this.put(`/api/folders/${folderId}`, { name: newName });
    }

    // 搜索API
    async searchFiles(query, engine = 'filename', page = 1, size = 20) {
        return this.get('/api/search/files', {
            q: query,
            engine,
            page,
            size
        });
    }

    async searchByImage(imageFile) {
        const formData = new FormData();
        formData.append('image', imageFile);
        return this.upload('/api/search/image', formData);
    }

    // 用户管理API
    async getUsers(page = 1, size = 20) {
        return this.get('/api/users/', { page, size });
    }

    async getUser(userId) {
        return this.get(`/api/users/${userId}`);
    }

    async updateUser(userId, userData) {
        return this.put(`/api/users/${userId}`, userData);
    }

    async deleteUser(userId) {
        return this.delete(`/api/users/${userId}`);
    }

    // 系统管理API
    async getSystemStats() {
        return this.get('/api/system/stats');
    }

    async getSystemHealth() {
        return this.get('/api/system/health');
    }

    async getActivityLogs(page = 1, size = 20) {
        return this.get('/api/admin/logs', { page, size });
    }

    async getSystemConfig() {
        return this.get('/api/admin/config');
    }

    async updateSystemConfig(config) {
        return this.put('/api/admin/config', config);
    }

    // 权限管理API
    async getUserPermissions(userId) {
        return this.get(`/api/permissions/user/${userId}`);
    }

    async setFilePermission(fileId, userId, permissionType, granted = true) {
        return this.post('/api/permissions/', {
            file_id: fileId,
            user_id: userId,
            permission_type: permissionType,
            is_granted: granted
        });
    }

    async removePermission(permissionId) {
        return this.delete(`/api/permissions/${permissionId}`);
    }
}

// 创建全局API客户端实例
const api = new ApiClient();

// 导出API客户端
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ApiClient, api };
}
