@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    Enterprise File Sharing System
echo    企业级文件共享系统
echo ========================================
echo.

cd /d "%~dp0"

echo [1/3] Checking Python / 检查Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found / Python未找到
    echo Please install Python 3.8+ / 请安装Python 3.8+
    pause
    exit /b 1
)
echo OK: Python found / Python已找到

echo.
echo [2/3] Installing dependencies / 安装依赖...
cd backend
pip install fastapi uvicorn sqlalchemy pymysql redis python-jose passlib loguru pydantic python-dotenv requests
if errorlevel 1 (
    echo ERROR: Failed to install dependencies / 依赖安装失败
    pause
    exit /b 1
)
echo OK: Dependencies installed / 依赖安装完成

echo.
echo [3/3] Initializing database / 初始化数据库...
python simple_init_db.py
if errorlevel 1 (
    echo ERROR: Database initialization failed / 数据库初始化失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Starting Server / 启动服务器
echo ========================================
echo.
echo Frontend: http://localhost:8000/static/index.html
echo API Docs: http://localhost:8000/docs
echo Admin: admin / admin123
echo.
echo Press Ctrl+C to stop / 按Ctrl+C停止服务
echo ========================================
echo.

python simple_run.py
