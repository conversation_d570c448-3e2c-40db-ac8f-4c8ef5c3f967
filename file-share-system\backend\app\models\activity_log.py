"""
活动日志模型
"""
from sqlalchemy import <PERSON>umn, Integer, String, <PERSON><PERSON><PERSON>, DateTime, Enum, <PERSON>Key, Text, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.database import Base


class ActionType(enum.Enum):
    """操作类型枚举"""
    LOGIN = "login"                 # 登录
    LOGOUT = "logout"               # 登出
    UPLOAD = "upload"               # 上传
    DOWNLOAD = "download"           # 下载
    DELETE = "delete"               # 删除
    SEARCH = "search"               # 搜索
    VIEW = "view"                   # 查看
    EDIT = "edit"                   # 编辑
    SHARE = "share"                 # 分享
    COPY = "copy"                   # 复制
    MOVE = "move"                   # 移动
    RENAME = "rename"               # 重命名
    CREATE_FOLDER = "create_folder" # 创建文件夹
    DELETE_FOLDER = "delete_folder" # 删除文件夹
    PERMISSION_CHANGE = "permission_change"  # 权限变更
    USER_CREATE = "user_create"     # 创建用户
    USER_DELETE = "user_delete"     # 删除用户
    USER_UPDATE = "user_update"     # 更新用户
    SYSTEM_CONFIG = "system_config" # 系统配置
    BACKUP = "backup"               # 备份
    RESTORE = "restore"             # 恢复


class TargetType(enum.Enum):
    """目标类型枚举"""
    FILE = "file"           # 文件
    FOLDER = "folder"       # 文件夹
    USER = "user"           # 用户
    SYSTEM = "system"       # 系统
    PERMISSION = "permission"  # 权限


class LogLevel(enum.Enum):
    """日志级别枚举"""
    INFO = "info"           # 信息
    WARNING = "warning"     # 警告
    ERROR = "error"         # 错误
    CRITICAL = "critical"   # 严重
    SECURITY = "security"   # 安全


class ActivityLog(Base):
    """活动日志模型"""
    __tablename__ = "activity_logs"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 操作信息
    action_type = Column(Enum(ActionType), nullable=False, index=True, comment="操作类型")
    target_type = Column(Enum(TargetType), nullable=False, index=True, comment="目标类型")
    target_id = Column(Integer, nullable=True, index=True, comment="目标ID")
    target_name = Column(String(255), nullable=True, comment="目标名称")
    
    # 用户信息
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True, comment="用户ID")
    username = Column(String(50), nullable=True, comment="用户名")
    
    # 网络信息
    ip_address = Column(String(45), nullable=True, index=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    session_id = Column(String(100), nullable=True, comment="会话ID")
    
    # 操作详情
    details = Column(Text, nullable=True, comment="操作详情JSON")
    result = Column(String(20), nullable=True, comment="操作结果")
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    # 文件相关信息
    file_path = Column(String(500), nullable=True, comment="文件路径")
    file_size = Column(Integer, nullable=True, comment="文件大小")
    file_type = Column(String(50), nullable=True, comment="文件类型")
    
    # 下载相关信息
    download_size = Column(Integer, nullable=True, comment="下载大小")
    download_duration = Column(Float, nullable=True, comment="下载耗时（秒）")
    download_speed = Column(Float, nullable=True, comment="下载速度（KB/s）")
    
    # 搜索相关信息
    search_query = Column(String(500), nullable=True, comment="搜索关键词")
    search_engine = Column(String(50), nullable=True, comment="搜索引擎")
    search_results_count = Column(Integer, nullable=True, comment="搜索结果数量")
    
    # 日志级别和标记
    log_level = Column(Enum(LogLevel), default=LogLevel.INFO, nullable=False, comment="日志级别")
    is_sensitive = Column(Boolean, default=False, nullable=False, comment="是否敏感操作")
    is_suspicious = Column(Boolean, default=False, nullable=False, comment="是否可疑操作")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, index=True, comment="创建时间")
    
    # 关联关系
    user = relationship("User", foreign_keys=[user_id])
    
    def __repr__(self):
        return f"<ActivityLog(id={self.id}, action='{self.action_type.value}', user_id={self.user_id})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "action_type": self.action_type.value,
            "target_type": self.target_type.value,
            "target_id": self.target_id,
            "target_name": self.target_name,
            "user_id": self.user_id,
            "username": self.username,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "session_id": self.session_id,
            "details": self.details,
            "result": self.result,
            "error_message": self.error_message,
            "file_path": self.file_path,
            "file_size": self.file_size,
            "file_type": self.file_type,
            "download_size": self.download_size,
            "download_duration": self.download_duration,
            "download_speed": self.download_speed,
            "search_query": self.search_query,
            "search_engine": self.search_engine,
            "search_results_count": self.search_results_count,
            "log_level": self.log_level.value,
            "is_sensitive": self.is_sensitive,
            "is_suspicious": self.is_suspicious,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class SystemLog(Base):
    """系统日志模型"""
    __tablename__ = "system_logs"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 日志信息
    log_level = Column(Enum(LogLevel), nullable=False, index=True, comment="日志级别")
    module = Column(String(100), nullable=False, comment="模块名称")
    message = Column(Text, nullable=False, comment="日志消息")
    
    # 详细信息
    details = Column(Text, nullable=True, comment="详细信息JSON")
    stack_trace = Column(Text, nullable=True, comment="堆栈跟踪")
    
    # 系统信息
    server_name = Column(String(100), nullable=True, comment="服务器名称")
    process_id = Column(Integer, nullable=True, comment="进程ID")
    thread_id = Column(Integer, nullable=True, comment="线程ID")
    
    # 性能信息
    cpu_usage = Column(Float, nullable=True, comment="CPU使用率")
    memory_usage = Column(Float, nullable=True, comment="内存使用率")
    disk_usage = Column(Float, nullable=True, comment="磁盘使用率")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, index=True, comment="创建时间")
    
    def __repr__(self):
        return f"<SystemLog(id={self.id}, level='{self.log_level.value}', module='{self.module}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "log_level": self.log_level.value,
            "module": self.module,
            "message": self.message,
            "details": self.details,
            "stack_trace": self.stack_trace,
            "server_name": self.server_name,
            "process_id": self.process_id,
            "thread_id": self.thread_id,
            "cpu_usage": self.cpu_usage,
            "memory_usage": self.memory_usage,
            "disk_usage": self.disk_usage,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class SecurityAlert(Base):
    """安全告警模型"""
    __tablename__ = "security_alerts"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 告警信息
    alert_type = Column(String(100), nullable=False, index=True, comment="告警类型")
    severity = Column(String(20), nullable=False, comment="严重程度")
    title = Column(String(255), nullable=False, comment="告警标题")
    description = Column(Text, nullable=False, comment="告警描述")
    
    # 相关信息
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, comment="相关用户ID")
    ip_address = Column(String(45), nullable=True, comment="相关IP地址")
    file_id = Column(Integer, ForeignKey("files.id"), nullable=True, comment="相关文件ID")
    
    # 告警详情
    details = Column(Text, nullable=True, comment="详细信息JSON")
    evidence = Column(Text, nullable=True, comment="证据信息")
    
    # 处理状态
    status = Column(String(20), default="open", nullable=False, comment="处理状态")
    handled_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="处理人ID")
    handled_at = Column(DateTime, nullable=True, comment="处理时间")
    handle_notes = Column(Text, nullable=True, comment="处理备注")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, index=True, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    
    # 关联关系
    user = relationship("User", foreign_keys=[user_id])
    file = relationship("File", foreign_keys=[file_id])
    handler = relationship("User", foreign_keys=[handled_by])
    
    def __repr__(self):
        return f"<SecurityAlert(id={self.id}, type='{self.alert_type}', severity='{self.severity}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "alert_type": self.alert_type,
            "severity": self.severity,
            "title": self.title,
            "description": self.description,
            "user_id": self.user_id,
            "ip_address": self.ip_address,
            "file_id": self.file_id,
            "details": self.details,
            "evidence": self.evidence,
            "status": self.status,
            "handled_by": self.handled_by,
            "handled_at": self.handled_at.isoformat() if self.handled_at else None,
            "handle_notes": self.handle_notes,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
