# -*- coding: utf-8 -*-
"""
Minimal startup script for testing
最小化启动脚本用于测试
"""
import os
import sys

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_minimal_app():
    """Create minimal FastAPI app for testing"""
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    
    app = FastAPI(
        title="Enterprise File Sharing System",
        version="1.0.0",
        description="Test version"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    @app.get("/")
    async def root():
        return {
            "message": "Enterprise File Sharing System is running!",
            "version": "1.0.0",
            "status": "OK"
        }
    
    @app.get("/health")
    async def health():
        return {
            "status": "healthy",
            "database": "not_tested",
            "redis": "not_tested"
        }
    
    @app.get("/test")
    async def test():
        return {
            "message": "Test endpoint working",
            "chinese": "中文测试正常",
            "encoding": "UTF-8"
        }
    
    return app

def main():
    """Main function"""
    print("=" * 60)
    print("Enterprise File Sharing System - Minimal Test")
    print("企业级文件共享系统 - 最小化测试")
    print("=" * 60)
    
    try:
        import uvicorn
        
        # Create the app
        app = create_minimal_app()
        
        print("Starting server...")
        print("正在启动服务器...")
        print("URL: http://localhost:8000")
        print("Test: http://localhost:8000/test")
        print("Health: http://localhost:8000/health")
        print("=" * 60)
        
        # Start server
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Please install: pip install fastapi uvicorn")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    main()
