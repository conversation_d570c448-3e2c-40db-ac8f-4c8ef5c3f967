@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    企业级文件共享系统 - 安装脚本
echo ========================================
echo.

cd /d "%~dp0"

echo [1/6] 检查管理员权限...
net session >nul 2>&1
if errorlevel 1 (
    echo ❌ 需要管理员权限运行此脚本
    echo 请右键点击脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)
echo ✅ 管理员权限确认

echo.
echo [2/6] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装
    echo 正在下载Python安装包...
    echo 请手动安装Python 3.8+并添加到PATH
    start https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python环境正常

echo.
echo [3/6] 检查MySQL安装...
mysql --version >nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL未安装
    echo 请安装MySQL 5.7+
    echo 下载地址: https://dev.mysql.com/downloads/mysql/
    pause
    exit /b 1
)
echo ✅ MySQL已安装

echo.
echo [4/6] 检查Redis安装...
redis-server --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Redis未安装
    echo 请安装Redis 6.0+
    echo 下载地址: https://github.com/microsoftarchive/redis/releases
    pause
    exit /b 1
)
echo ✅ Redis已安装

echo.
echo [5/6] 创建虚拟环境...
cd backend
if exist "venv" (
    echo 虚拟环境已存在，跳过创建
) else (
    python -m venv venv
    echo ✅ 虚拟环境创建完成
)

echo.
echo [6/6] 安装Python依赖...
call venv\Scripts\activate.bat
pip install --upgrade pip
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)
echo ✅ 依赖安装完成

echo.
echo ========================================
echo    🎉 安装完成！
echo ========================================
echo.
echo 📋 下一步操作:
echo 1. 启动MySQL服务
echo 2. 启动Redis服务  
echo 3. 创建数据库: CREATE DATABASE file_share_system;
echo 4. 运行 start.bat 启动系统
echo.
echo 📝 配置文件位置: backend\.env
echo 📚 详细说明请查看: README.md
echo.
pause
