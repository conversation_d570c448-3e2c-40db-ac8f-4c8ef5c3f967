

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   FastAPI后端   │    │   MySQL数据库   │
│   (HTML/JS)     │◄──►│   (Python)      │◄──►│   (主数据存储)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                         │
                              ▼                         ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Redis缓存     │    │   文件存储      │
                       │   (会话/缓存)   │    │   (本地磁盘)    │
                       └─────────────────┘    └─────────────────┘
```

### 2.2 目录结构
```
file-share-system/
├── backend/                 # 后端代码
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py         # FastAPI应用入口
│   │   ├── config.py       # 配置文件
│   │   ├── database.py     # 数据库连接
│   │   ├── models/         # 数据模型
│   │   ├── routers/        # API路由
│   │   ├── services/       # 业务逻辑
│   │   ├── utils/          # 工具函数
│   │   └── middleware/     # 中间件
│   ├── requirements.txt    # Python依赖
│   └── run.py             # 启动脚本
├── frontend/               # 前端代码
│   ├── static/
│   │   ├── css/
│   │   ├── js/
│   │   └── images/
│   └── templates/
├── storage/               # 文件存储目录
│   ├── uploads/          # 上传文件
│   ├── thumbnails/       # 缩略图
│   └── temp/            # 临时文件
├── logs/                 # 日志文件
├── config/              # 配置文件
└── docs/               # 文档
```

## 3. 数据库设计

### 3.1 用户表 (users)
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    role ENUM('admin', 'user', 'readonly') DEFAULT 'user',
    group_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL
);
```

### 3.2 文件表 (files)
```sql
CREATE TABLE files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(50),
    mime_type VARCHAR(100),
    md5_hash VARCHAR(32),
    thumbnail_path VARCHAR(500),
    folder_id INT,
    uploaded_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT FALSE,
    INDEX idx_filename (filename),
    INDEX idx_file_type (file_type),
    INDEX idx_folder_id (folder_id)
);
```

### 3.3 权限表 (permissions)
```sql
CREATE TABLE permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    file_id INT,
    folder_id INT,
    permission_type ENUM('read', 'write', 'delete', 'download', 'upload') NOT NULL,
    granted_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_file (user_id, file_id),
    INDEX idx_user_folder (user_id, folder_id)
);
```

### 3.4 操作日志表 (activity_logs)
```sql
CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action_type ENUM('login', 'logout', 'upload', 'download', 'delete', 'search', 'view') NOT NULL,
    target_type ENUM('file', 'folder', 'system') NOT NULL,
    target_id INT,
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_action (user_id, action_type),
    INDEX idx_created_at (created_at)
);
```

## 4. 核心功能实现

### 4.1 权限管理系统
- **角色权限**: 管理员、普通用户、只读用户
- **文件权限**: 读取、写入、删除、下载、上传
- **网络权限**: 内网访问、外网访问
- **权限继承**: 文件夹权限自动继承给子文件

### 4.2 双搜索引擎
#### 4.2.1 文件名搜索引擎
- 基于MySQL全文索引
- 支持模糊搜索、正则表达式
- 搜索结果按相关度排序
- 搜索历史记录

#### 4.2.2 图像识别搜索引擎
- 使用OpenCV进行图像特征提取
- 支持的格式: JPG, PNG, TIF, PSD, AI, EPS
- 特征向量存储在数据库中
- 相似度计算和匹配

### 4.3 文件管理
- **上传**: 支持单文件、多文件、拖拽上传
- **下载**: 单文件下载、批量下载、文件夹打包下载
- **预览**: 图片预览、缩略图生成
- **版本控制**: 文件版本管理和回滚

### 4.4 安全机制
- **文件加密**: 下载文件自动加密
- **密码申请**: 解压密码申请机制
- **访问控制**: IP白名单、访问频率限制
- **敏感文件**: 敏感文件标记和监控

## 5. 系统扩展性规划

### 5.1 水平扩展
- 数据库读写分离
- Redis集群部署
- 文件存储分布式部署
- 负载均衡配置

### 5.2 功能扩展
- 文件版本管理
- 在线编辑功能
- 移动端适配
- API接口开放

### 5.3 性能优化
- 数据库索引优化
- 缓存策略优化
- 文件传输优化
- 图像处理优化

## 6. 内外网访问权限控制

### 6.1 网络访问控制
- **内网访问**: 默认允许内网IP段访问
- **外网访问**: 需要管理员开启，支持IP白名单
- **动态权限**: 不同文件夹可设置不同网络访问权限

### 6.2 安全策略
- **VPN集成**: 支持VPN用户访问
- **SSL加密**: 强制HTTPS访问
- **防火墙规则**: 自动配置防火墙规则

## 7. 监控和统计

### 7.1 实时监控
- 在线用户数量和状态
- 系统资源使用情况
- 文件操作实时监控
- 异常行为检测

### 7.2 统计报表
- 用户行为统计
- 文件访问热度
- 存储使用分析
- 性能指标监控

## 8. 部署和维护

### 8.1 Windows部署
- Python环境配置
- MySQL数据库安装
- Redis服务安装
- 系统服务注册

### 8.2 远程管理
- Web管理界面
- 远程配置更新
- 日志远程查看
- 系统状态监控

### 8.3 备份策略
- 数据库定期备份
- 文件增量备份
- 配置文件备份
- 灾难恢复方案

## 9. 开发规范

### 9.1 代码规范
- PEP 8 Python代码规范
- 函数和类命名规范
- 注释和文档规范
- 错误处理规范

### 9.2 测试规范
- 单元测试覆盖率 > 80%
- 集成测试用例
- 性能测试基准
- 安全测试检查

### 9.3 版本管理
- Git分支管理策略
- 版本号命名规范
- 发布流程规范
- 回滚策略制定
