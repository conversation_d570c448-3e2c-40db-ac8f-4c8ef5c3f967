// 文件管理功能

// 文件上传处理
async function handleFileUpload(files) {
    if (!files || files.length === 0) return;
    
    const uploadProgress = document.getElementById('upload-progress');
    const progressFill = document.getElementById('progress-fill');
    const uploadStatus = document.getElementById('upload-status');
    
    uploadProgress.style.display = 'block';
    
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        try {
            uploadStatus.textContent = `正在上传: ${file.name} (${i + 1}/${files.length})`;
            
            await api.uploadFile(file, app.currentFolder, (progress) => {
                progressFill.style.width = progress + '%';
            });
            
            app.showNotification(`${file.name} 上传成功`, 'success');
            
        } catch (error) {
            console.error('上传失败:', error);
            app.showNotification(`${file.name} 上传失败: ${error.message}`, 'error');
        }
    }
    
    // 刷新文件列表
    await app.loadFiles(app.currentFolder);
    
    // 关闭上传模态框
    closeModal();
}

// 初始化上传区域事件
function initUploadAreaEvents() {
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');
    
    // 点击上传区域选择文件
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });
    
    // 文件选择事件
    fileInput.addEventListener('change', (e) => {
        handleFileUpload(e.target.files);
    });
    
    // 拖拽事件
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
    });
    
    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, () => {
            uploadArea.classList.add('dragover');
        }, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, () => {
            uploadArea.classList.remove('dragover');
        }, false);
    });
    
    uploadArea.addEventListener('drop', (e) => {
        const files = e.dataTransfer.files;
        handleFileUpload(files);
    }, false);
}

// 创建文件夹
async function handleCreateFolder() {
    const folderName = document.getElementById('folder-name').value.trim();
    
    if (!folderName) {
        app.showNotification('请输入文件夹名称', 'error');
        return;
    }
    
    try {
        await api.createFolder(folderName, app.currentFolder);
        app.showNotification('文件夹创建成功', 'success');
        
        // 刷新文件列表
        await app.loadFiles(app.currentFolder);
        
        // 关闭模态框
        closeModal();
        
    } catch (error) {
        console.error('创建文件夹失败:', error);
        app.showNotification(error.message || '创建文件夹失败', 'error');
    }
}

// 文件操作菜单
function showFileMenu(event, fileId) {
    event.stopPropagation();
    
    const menuItems = [
        {
            icon: '📥',
            text: '下载',
            onclick: `downloadFile(${fileId})`
        },
        {
            icon: '✏️',
            text: '重命名',
            onclick: `renameFile(${fileId})`
        },
        {
            icon: '📋',
            text: '复制链接',
            onclick: `copyFileLink(${fileId})`
        },
        {
            separator: true
        },
        {
            icon: '🗑️',
            text: '删除',
            class: 'danger',
            onclick: `deleteFile(${fileId})`
        }
    ];
    
    showContextMenu(event, menuItems);
}

// 下载文件
async function downloadFile(fileId) {
    try {
        const response = await api.downloadFile(fileId);
        // 处理下载响应
        app.showNotification('开始下载文件', 'success');
    } catch (error) {
        console.error('下载失败:', error);
        app.showNotification('下载失败: ' + error.message, 'error');
    }
}

// 重命名文件
function renameFile(fileId) {
    const fileItem = document.querySelector(`[data-file-id="${fileId}"]`);
    const currentName = fileItem.querySelector('.file-name').textContent;
    
    showInputDialog('重命名文件', '请输入新的文件名', currentName, async (newName) => {
        try {
            await api.renameFile(fileId, newName);
            app.showNotification('重命名成功', 'success');
            await app.loadFiles(app.currentFolder);
        } catch (error) {
            console.error('重命名失败:', error);
            app.showNotification('重命名失败: ' + error.message, 'error');
        }
    });
}

// 复制文件链接
async function copyFileLink(fileId) {
    try {
        const link = `${window.location.origin}/api/files/${fileId}/download`;
        await copyToClipboard(link);
        app.showNotification('链接已复制到剪贴板', 'success');
    } catch (error) {
        app.showNotification('复制链接失败', 'error');
    }
}

// 删除文件
function deleteFile(fileId) {
    const fileItem = document.querySelector(`[data-file-id="${fileId}"]`);
    const fileName = fileItem.querySelector('.file-name').textContent;
    
    showConfirmDialog(
        '确认删除',
        `确定要删除文件 "${fileName}" 吗？此操作不可撤销。`,
        async () => {
            try {
                await api.deleteFile(fileId);
                app.showNotification('文件删除成功', 'success');
                await app.loadFiles(app.currentFolder);
            } catch (error) {
                console.error('删除失败:', error);
                app.showNotification('删除失败: ' + error.message, 'error');
            }
        }
    );
}

// 打开文件夹
async function openFolder(folderId) {
    app.currentFolder = folderId;
    await app.loadFiles(folderId);
}

// 打开文件
function openFile(fileId) {
    // 在新窗口中打开文件
    window.open(`/api/files/${fileId}/view`, '_blank');
}

// 文件选择相关
function selectFile(item) {
    item.classList.add('selected');
    app.selectedFiles.add(item.dataset.fileId);
}

function toggleFileSelection(item) {
    if (item.classList.contains('selected')) {
        item.classList.remove('selected');
        app.selectedFiles.delete(item.dataset.fileId);
    } else {
        item.classList.add('selected');
        app.selectedFiles.add(item.dataset.fileId);
    }
}

function clearFileSelection() {
    document.querySelectorAll('.file-item.selected').forEach(item => {
        item.classList.remove('selected');
    });
    app.selectedFiles.clear();
}

// 切换视图模式
function switchView(viewType) {
    app.currentView = viewType;
    
    // 更新按钮状态
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.view === viewType);
    });
    
    // 更新文件网格类
    const fileGrid = document.getElementById('file-grid');
    fileGrid.className = `file-${viewType}`;
}

// 更改排序顺序
async function changeSortOrder(sortType) {
    app.currentSort = sortType;
    await app.loadFiles(app.currentFolder);
}

// 加载文件夹树
async function loadFolderTree() {
    try {
        const folders = await api.getFolders();
        renderFolderTree(folders);
    } catch (error) {
        console.error('加载文件夹树失败:', error);
    }
}

// 渲染文件夹树
function renderFolderTree(folders) {
    const folderTree = document.getElementById('folder-tree');
    
    // 清空现有内容，保留根目录
    const rootItem = folderTree.querySelector('.folder-item[data-folder-id="0"]');
    folderTree.innerHTML = '';
    folderTree.appendChild(rootItem);
    
    // 添加文件夹
    folders.forEach(folder => {
        const folderItem = document.createElement('div');
        folderItem.className = 'folder-item';
        folderItem.dataset.folderId = folder.id;
        folderItem.innerHTML = `
            <span class="folder-icon">📁</span>
            <span class="folder-name">${folder.name}</span>
        `;
        
        folderItem.addEventListener('click', () => {
            // 移除其他活动状态
            document.querySelectorAll('.folder-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 设置当前活动状态
            folderItem.classList.add('active');
            
            // 加载文件夹内容
            openFolder(folder.id);
        });
        
        folderTree.appendChild(folderItem);
    });
}

// 更新面包屑导航
function updateBreadcrumb(path) {
    const breadcrumb = document.getElementById('breadcrumb');
    
    if (!path || path.length === 0) {
        breadcrumb.innerHTML = '<span class="breadcrumb-item active">根目录</span>';
        return;
    }
    
    const items = [
        { text: '根目录', onclick: 'openFolder(null)' },
        ...path.map(folder => ({
            text: folder.name,
            onclick: `openFolder(${folder.id})`
        }))
    ];
    
    breadcrumb.innerHTML = items.map((item, index) => {
        const isLast = index === items.length - 1;
        return `
            <span class="breadcrumb-item ${isLast ? 'active' : ''}" ${!isLast ? `onclick="${item.onclick}"` : ''}>
                ${item.text}
            </span>
            ${!isLast ? '<span class="breadcrumb-separator">></span>' : ''}
        `;
    }).join('');
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        handleFileUpload,
        initUploadAreaEvents,
        handleCreateFolder,
        showFileMenu,
        downloadFile,
        renameFile,
        copyFileLink,
        deleteFile,
        openFolder,
        openFile,
        selectFile,
        toggleFileSelection,
        clearFileSelection,
        switchView,
        changeSortOrder,
        loadFolderTree,
        renderFolderTree,
        updateBreadcrumb
    };
}
