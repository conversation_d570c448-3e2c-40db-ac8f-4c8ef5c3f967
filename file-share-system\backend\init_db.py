# -*- coding: utf-8 -*-
"""
Database Initialization Script
数据库初始化脚本
"""
import os
import sys
from datetime import datetime
from loguru import logger

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config import get_settings
from app.database import init_database, get_db
from app.models.user import User, UserGroup, UserRole
from app.models.system_config import SystemConfig

settings = get_settings()


def create_default_admin():
    """创建默认管理员账户"""
    from passlib.context import CryptContext
    
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    db = next(get_db())
    try:
        # 检查是否已存在管理员
        admin_exists = db.query(User).filter(User.role == UserRole.ADMIN).first()
        if admin_exists:
            logger.info("管理员账户已存在，跳过创建")
            return
        
        # 创建默认管理员
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            password_hash=pwd_context.hash("admin123"),
            full_name="系统管理员",
            role=UserRole.ADMIN,
            is_active=True,
            is_verified=True,
            internal_access=True,
            external_access=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        logger.info(f"✅ 默认管理员账户创建成功: admin / admin123")
        
    except Exception as e:
        db.rollback()
        logger.error(f"❌ 创建默认管理员失败: {e}")
        raise
    finally:
        db.close()


def create_default_user_groups():
    """创建默认用户组"""
    db = next(get_db())
    try:
        # 检查是否已存在用户组
        groups_exist = db.query(UserGroup).first()
        if groups_exist:
            logger.info("用户组已存在，跳过创建")
            return
        
        # 创建默认用户组
        default_groups = [
            {
                "name": "管理员组",
                "description": "系统管理员用户组",
                "default_role": UserRole.ADMIN,
                "default_internal_access": True,
                "default_external_access": True,
                "default_max_download_files": 100,
                "default_max_download_size_mb": 5000,
                "default_download_speed_limit_kb": 10240
            },
            {
                "name": "普通用户组",
                "description": "普通用户组",
                "default_role": UserRole.USER,
                "default_internal_access": True,
                "default_external_access": False,
                "default_max_download_files": 10,
                "default_max_download_size_mb": 500,
                "default_download_speed_limit_kb": 1024
            },
            {
                "name": "只读用户组",
                "description": "只读权限用户组",
                "default_role": UserRole.READONLY,
                "default_internal_access": True,
                "default_external_access": False,
                "default_max_download_files": 5,
                "default_max_download_size_mb": 100,
                "default_download_speed_limit_kb": 512
            }
        ]
        
        for group_data in default_groups:
            group = UserGroup(**group_data)
            db.add(group)
        
        db.commit()
        logger.info("✅ 默认用户组创建成功")
        
    except Exception as e:
        db.rollback()
        logger.error(f"❌ 创建默认用户组失败: {e}")
        raise
    finally:
        db.close()


def create_default_system_configs():
    """创建默认系统配置"""
    db = next(get_db())
    try:
        # 检查是否已存在系统配置
        configs_exist = db.query(SystemConfig).first()
        if configs_exist:
            logger.info("系统配置已存在，跳过创建")
            return
        
        # 创建默认系统配置
        default_configs = [
            {
                "config_key": "system.name",
                "config_value": "企业级文件共享系统",
                "config_type": "string",
                "category": "system",
                "display_name": "系统名称",
                "description": "系统显示名称"
            },
            {
                "config_key": "system.version",
                "config_value": "1.0.0",
                "config_type": "string",
                "category": "system",
                "display_name": "系统版本",
                "description": "当前系统版本号"
            },
            {
                "config_key": "user.registration_enabled",
                "config_value": "true",
                "config_type": "boolean",
                "category": "user",
                "display_name": "允许用户注册",
                "description": "是否允许新用户注册"
            },
            {
                "config_key": "file.max_upload_size",
                "config_value": "104857600",
                "config_type": "integer",
                "category": "file",
                "display_name": "最大上传文件大小",
                "description": "单个文件最大上传大小（字节）"
            },
            {
                "config_key": "search.filename_engine_enabled",
                "config_value": "true",
                "config_type": "boolean",
                "category": "search",
                "display_name": "文件名搜索引擎",
                "description": "是否启用文件名搜索引擎"
            },
            {
                "config_key": "search.image_engine_enabled",
                "config_value": "true",
                "config_type": "boolean",
                "category": "search",
                "display_name": "图像搜索引擎",
                "description": "是否启用图像识别搜索引擎"
            },
            {
                "config_key": "security.external_access_enabled",
                "config_value": "false",
                "config_type": "boolean",
                "category": "security",
                "display_name": "外网访问",
                "description": "是否允许外网访问"
            },
            {
                "config_key": "download.encryption_enabled",
                "config_value": "true",
                "config_type": "boolean",
                "category": "download",
                "display_name": "下载加密",
                "description": "是否启用下载文件加密"
            },
            {
                "config_key": "download.encryption_trigger_count",
                "config_value": "3",
                "config_type": "integer",
                "category": "download",
                "display_name": "加密触发次数",
                "description": "下载多少次后开始加密"
            },
            {
                "config_key": "notification.banner_text",
                "config_value": "欢迎使用企业级文件共享系统！",
                "config_type": "string",
                "category": "notification",
                "display_name": "通知横幅文本",
                "description": "系统顶部滚动通知文本"
            }
        ]
        
        for config_data in default_configs:
            config = SystemConfig(**config_data)
            db.add(config)
        
        db.commit()
        logger.info("✅ 默认系统配置创建成功")
        
    except Exception as e:
        db.rollback()
        logger.error(f"❌ 创建默认系统配置失败: {e}")
        raise
    finally:
        db.close()


def main():
    """主函数"""
    logger.info("🔧 开始初始化数据库...")
    
    try:
        # 初始化数据库表结构
        logger.info("📋 创建数据库表结构...")
        init_database()
        logger.info("✅ 数据库表结构创建成功")
        
        # 创建默认数据
        logger.info("👤 创建默认管理员账户...")
        create_default_admin()
        
        logger.info("👥 创建默认用户组...")
        create_default_user_groups()
        
        logger.info("⚙️ 创建默认系统配置...")
        create_default_system_configs()
        
        logger.info("🎉 数据库初始化完成!")
        logger.info("📝 默认管理员账户: admin / admin123")
        logger.info("🚀 现在可以启动系统了: python run.py")
        
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
