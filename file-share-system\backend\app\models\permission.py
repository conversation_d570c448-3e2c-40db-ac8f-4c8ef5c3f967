"""
权限模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Enum, ForeignKey, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.database import Base


class PermissionType(enum.Enum):
    """权限类型枚举"""
    READ = "read"           # 读取权限
    WRITE = "write"         # 写入权限
    DELETE = "delete"       # 删除权限
    DOWNLOAD = "download"   # 下载权限
    UPLOAD = "upload"       # 上传权限
    SHARE = "share"         # 分享权限
    ADMIN = "admin"         # 管理权限


class Permission(Base):
    """权限模型"""
    __tablename__ = "permissions"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 权限主体
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True, comment="用户ID")
    group_id = Column(Integer, ForeignKey("user_groups.id"), nullable=True, index=True, comment="用户组ID")
    
    # 权限对象
    file_id = Column(Integer, ForeignKey("files.id"), nullable=True, index=True, comment="文件ID")
    folder_id = Column(Integer, ForeignKey("folders.id"), nullable=True, index=True, comment="文件夹ID")
    
    # 权限类型
    permission_type = Column(Enum(PermissionType), nullable=False, index=True, comment="权限类型")
    
    # 权限状态
    is_granted = Column(Boolean, default=True, nullable=False, comment="是否授予权限")
    is_inherited = Column(Boolean, default=False, nullable=False, comment="是否继承权限")
    
    # 权限限制
    max_downloads = Column(Integer, nullable=True, comment="最大下载次数")
    max_download_size_mb = Column(Integer, nullable=True, comment="最大下载大小MB")
    download_speed_limit_kb = Column(Integer, nullable=True, comment="下载速度限制KB/s")
    
    # 时间限制
    valid_from = Column(DateTime, nullable=True, comment="权限生效时间")
    valid_until = Column(DateTime, nullable=True, comment="权限过期时间")
    
    # 网络限制
    ip_whitelist = Column(Text, nullable=True, comment="IP白名单JSON")
    internal_only = Column(Boolean, default=False, nullable=False, comment="仅限内网")
    
    # 授权信息
    granted_by = Column(Integer, ForeignKey("users.id"), nullable=False, comment="授权者ID")
    granted_reason = Column(Text, nullable=True, comment="授权原因")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    
    # 关联关系
    user = relationship("User", foreign_keys=[user_id])
    file = relationship("File", foreign_keys=[file_id])
    folder = relationship("Folder", foreign_keys=[folder_id])
    granter = relationship("User", foreign_keys=[granted_by])
    
    def __repr__(self):
        return f"<Permission(id={self.id}, type='{self.permission_type.value}', user_id={self.user_id})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "group_id": self.group_id,
            "file_id": self.file_id,
            "folder_id": self.folder_id,
            "permission_type": self.permission_type.value,
            "is_granted": self.is_granted,
            "is_inherited": self.is_inherited,
            "max_downloads": self.max_downloads,
            "max_download_size_mb": self.max_download_size_mb,
            "download_speed_limit_kb": self.download_speed_limit_kb,
            "valid_from": self.valid_from.isoformat() if self.valid_from else None,
            "valid_until": self.valid_until.isoformat() if self.valid_until else None,
            "internal_only": self.internal_only,
            "granted_by": self.granted_by,
            "granted_reason": self.granted_reason,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    def is_valid(self) -> bool:
        """检查权限是否有效"""
        if not self.is_granted:
            return False
        
        from datetime import datetime
        now = datetime.now()
        
        if self.valid_from and now < self.valid_from:
            return False
        
        if self.valid_until and now > self.valid_until:
            return False
        
        return True


class PermissionTemplate(Base):
    """权限模板模型"""
    __tablename__ = "permission_templates"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(100), unique=True, nullable=False, comment="模板名称")
    description = Column(Text, nullable=True, comment="模板描述")
    
    # 模板权限配置
    permissions = Column(Text, nullable=False, comment="权限配置JSON")
    
    # 默认限制
    default_max_downloads = Column(Integer, nullable=True, comment="默认最大下载次数")
    default_max_download_size_mb = Column(Integer, nullable=True, comment="默认最大下载大小MB")
    default_download_speed_limit_kb = Column(Integer, nullable=True, comment="默认下载速度限制KB/s")
    
    # 网络限制
    default_internal_only = Column(Boolean, default=False, nullable=False, comment="默认仅限内网")
    
    # 状态
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    
    # 创建信息
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False, comment="创建者ID")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    
    # 关联关系
    creator = relationship("User", foreign_keys=[created_by])
    
    def __repr__(self):
        return f"<PermissionTemplate(id={self.id}, name='{self.name}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "permissions": self.permissions,
            "default_max_downloads": self.default_max_downloads,
            "default_max_download_size_mb": self.default_max_download_size_mb,
            "default_download_speed_limit_kb": self.default_download_speed_limit_kb,
            "default_internal_only": self.default_internal_only,
            "is_active": self.is_active,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class AccessControl(Base):
    """访问控制模型"""
    __tablename__ = "access_controls"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 控制类型
    control_type = Column(String(50), nullable=False, index=True, comment="控制类型")
    
    # 控制对象
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True, comment="用户ID")
    ip_address = Column(String(45), nullable=True, index=True, comment="IP地址")
    ip_range = Column(String(100), nullable=True, comment="IP范围")
    
    # 控制规则
    is_allowed = Column(Boolean, default=True, nullable=False, comment="是否允许")
    rule_description = Column(Text, nullable=True, comment="规则描述")
    
    # 时间限制
    valid_from = Column(DateTime, nullable=True, comment="生效时间")
    valid_until = Column(DateTime, nullable=True, comment="过期时间")
    
    # 创建信息
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False, comment="创建者ID")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    
    # 关联关系
    user = relationship("User", foreign_keys=[user_id])
    creator = relationship("User", foreign_keys=[created_by])
    
    def __repr__(self):
        return f"<AccessControl(id={self.id}, type='{self.control_type}', allowed={self.is_allowed})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "control_type": self.control_type,
            "user_id": self.user_id,
            "ip_address": self.ip_address,
            "ip_range": self.ip_range,
            "is_allowed": self.is_allowed,
            "rule_description": self.rule_description,
            "valid_from": self.valid_from.isoformat() if self.valid_from else None,
            "valid_until": self.valid_until.isoformat() if self.valid_until else None,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    def is_valid(self) -> bool:
        """检查访问控制是否有效"""
        from datetime import datetime
        now = datetime.now()
        
        if self.valid_from and now < self.valid_from:
            return False
        
        if self.valid_until and now > self.valid_until:
            return False
        
        return True
