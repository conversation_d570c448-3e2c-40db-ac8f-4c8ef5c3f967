# 企业级文件共享系统 - 各阶段开发提示词

## 阶段1: 项目初始化和基础架构搭建

### 提示词1.1: 项目结构创建
```
请帮我创建一个企业级文件共享系统的项目结构。要求：
1. 使用Python + FastAPI作为后端框架
2. 使用MySQL作为主数据库，Redis作为缓存
3. 前端使用原生HTML/CSS/JavaScript
4. 创建完整的目录结构，包括backend、frontend、storage、logs、config等目录
5. 生成requirements.txt文件，包含所有必要的Python依赖
6. 创建基础的配置文件和启动脚本
7. 确保项目可以在Windows环境下直接部署，不使用Docker

请按照以下目录结构创建：
- backend/ (后端代码)
- frontend/ (前端代码)
- storage/ (文件存储)
- logs/ (日志文件)
- config/ (配置文件)
- docs/ (文档)
```

### 提示词1.2: 数据库设计和初始化
```
请帮我设计并创建企业级文件共享系统的数据库结构。要求：
1. 创建用户表(users)，包含用户信息、角色、权限、登录状态等字段
2. 创建文件表(files)，包含文件信息、路径、大小、类型、上传者等字段
3. 创建权限表(permissions)，支持细粒度权限控制
4. 创建操作日志表(activity_logs)，记录所有用户操作
5. 创建文件夹表(folders)，支持目录结构管理
6. 创建系统配置表(system_config)，存储系统设置
7. 添加必要的索引以优化查询性能
8. 创建数据库初始化脚本和示例数据
9. 考虑数据库的扩展性和性能优化

请提供完整的SQL建表语句和Python数据库连接代码。
```

### 提示词1.3: FastAPI基础框架搭建
```
请帮我搭建FastAPI基础框架。要求：
1. 创建main.py作为应用入口
2. 配置数据库连接池(MySQL + Redis)
3. 设置CORS中间件支持跨域访问
4. 创建用户认证中间件(JWT Token)
5. 设置请求日志中间件
6. 创建基础的路由结构：
   - /auth (认证相关)
   - /files (文件操作)
   - /users (用户管理)
   - /admin (管理功能)
   - /search (搜索功能)
7. 创建统一的响应格式和错误处理
8. 配置静态文件服务
9. 添加API文档自动生成
10. 确保代码结构清晰，易于维护和扩展

请提供完整的代码实现和配置说明。
```

## 阶段2: 用户认证和权限管理

### 提示词2.1: 用户认证系统
```
请帮我实现用户认证系统。要求：
1. 用户注册功能（管理员可控制开启/关闭）
2. 用户登录功能，支持用户名/邮箱登录
3. JWT Token生成和验证
4. 密码加密存储（使用bcrypt）
5. 登录失败次数限制和账户锁定机制
6. 记住登录状态功能
7. 用户会话管理（Redis存储）
8. 登录日志记录
9. 密码强度验证
10. 支持管理员重置用户密码

请提供完整的认证相关API接口和前端登录页面。
```

### 提示词2.2: 权限管理系统
```
请帮我实现细粒度的权限管理系统。要求：
1. 角色权限系统：管理员、普通用户、只读用户
2. 文件级权限控制：读取、写入、删除、下载、上传
3. 文件夹级权限继承机制
4. 用户组管理功能
5. 权限检查装饰器和中间件
6. 动态权限分配和回收
7. 权限变更日志记录
8. 批量权限设置功能
9. 权限模板功能
10. 内外网访问权限控制

请提供权限检查的核心代码和管理界面。
```

### 提示词2.3: 用户管理界面
```
请帮我创建用户管理界面。要求：
1. 用户列表展示（支持分页、搜索、筛选）
2. 用户信息编辑功能
3. 用户权限设置界面
4. 用户组管理功能
5. 批量用户操作（启用/禁用/删除）
6. 用户登录历史查看
7. 用户行为统计展示
8. 在线用户实时显示
9. 用户导入/导出功能
10. 响应式设计，支持移动端访问

请提供完整的HTML页面和JavaScript交互代码。
```

## 阶段3: 文件管理核心功能

### 提示词3.1: 文件上传功能
```
请帮我实现文件上传功能。要求：
1. 支持单文件和多文件上传
2. 支持拖拽上传
3. 大文件分片上传和断点续传
4. 上传进度显示
5. 文件类型和大小限制
6. 文件重名处理策略
7. 上传文件病毒扫描
8. 缩略图自动生成（图片文件）
9. 文件MD5校验防重复
10. 上传权限检查
11. 支持的文件格式：JPG, PNG, TIF, PSD, AI, EPS等

请提供后端上传API和前端上传组件。
```

### 提示词3.2: 文件下载和预览
```
请帮我实现文件下载和预览功能。要求：
1. 单文件下载
2. 批量文件下载（ZIP打包）
3. 文件夹打包下载
4. 下载权限验证
5. 下载次数统计和限制
6. 下载速度限制（防止占用过多带宽）
7. 文件预览功能（图片、PDF、文本）
8. 缩略图展示（超大、大、中等图标模式）
9. 文件加密下载（可配置加密次数）
10. 下载密码申请机制
11. 下载日志记录

请提供完整的下载API和前端预览界面。
```

### 提示词3.3: 文件管理界面
```
请帮我创建文件管理界面。要求：
1. 文件列表展示（网格视图、列表视图）
2. 文件夹树形结构导航
3. 文件搜索和筛选功能
4. 文件排序（名称、大小、时间、类型）
5. 文件批量操作（选择、删除、移动、复制）
6. 文件详情查看
7. 文件重命名功能
8. 文件夹创建和管理
9. 面包屑导航
10. 右键菜单功能
11. 响应式设计适配不同屏幕

请提供完整的文件管理界面和交互逻辑。
```

## 阶段4: 双搜索引擎实现

### 提示词4.1: 文件名搜索引擎
```
请帮我实现高性能的文件名搜索引擎。要求：
1. 基于MySQL全文索引的快速搜索
2. 支持模糊搜索、精确搜索、正则表达式搜索
3. 搜索结果按相关度排序
4. 搜索历史记录和热门搜索
5. 搜索建议和自动补全
6. 高级搜索功能（文件类型、大小、时间范围）
7. 搜索结果分页和无限滚动
8. 搜索性能优化（缓存、索引）
9. 搜索统计和分析
10. 支持中文分词搜索

请提供搜索API接口和前端搜索组件，确保搜索速度达到Everything级别。
```

### 提示词4.2: 图像识别搜索引擎
```
请帮我实现图像识别搜索引擎。要求：
1. 使用OpenCV进行图像特征提取
2. 支持多种图像格式：JPG, PNG, TIF, PSD, AI, EPS
3. 图像相似度计算和匹配算法
4. 特征向量存储和索引优化
5. 以图搜图功能
6. 图像内容识别（物体、场景、颜色）
7. 图像质量评估
8. 批量图像特征提取
9. 搜索结果相似度排序
10. 图像搜索性能优化

请提供图像处理核心算法和搜索接口，确保识图搜索快速准确。
```

### 提示词4.3: 搜索界面集成
```
请帮我创建统一的搜索界面。要求：
1. 双搜索引擎切换功能
2. 搜索框智能提示
3. 搜索结果统一展示
4. 搜索筛选器（文件类型、时间、大小）
5. 搜索结果导出功能
6. 搜索历史管理
7. 保存搜索条件功能
8. 搜索结果预览
9. 搜索性能监控
10. 管理员搜索引擎开关控制

请提供完整的搜索界面和交互逻辑。
```

## 阶段5: 系统监控和管理

### 提示词5.1: 实时监控系统
```
请帮我实现系统实时监控功能。要求：
1. 在线用户实时统计和显示
2. 系统资源监控（CPU、内存、磁盘、网络）
3. 文件操作实时监控
4. 异常行为检测和告警
5. 性能指标实时展示
6. 系统日志实时查看
7. 数据库连接池监控
8. 缓存使用情况监控
9. 文件存储空间监控
10. 网络流量监控

请提供监控数据收集API和实时监控仪表板。
```

### 提示词5.2: 日志和统计系统
```
请帮我实现完整的日志和统计系统。要求：
1. 用户行为日志记录（登录、搜索、下载、上传）
2. 系统操作日志记录
3. 错误日志记录和分析
4. 日志分级和轮转
5. 用户行为统计分析
6. 文件访问热度统计
7. 系统使用情况报表
8. 日志搜索和筛选功能
9. 日志导出功能
10. 敏感操作重点标注和警告

请提供日志记录框架和统计分析界面。
```

### 提示词5.3: 管理员控制面板
```
请帮我创建管理员控制面板。要求：
1. 系统概览仪表板
2. 用户管理功能
3. 文件管理功能
4. 权限配置界面
5. 系统设置管理
6. 日志查看和分析
7. 监控告警设置
8. 备份和恢复功能
9. 系统维护工具
10. 远程管理功能

请提供完整的管理界面和功能实现。
```

## 阶段6: 安全和优化

### 提示词6.1: 安全机制实现
```
请帮我实现系统安全机制。要求：
1. 文件下载加密功能
2. 解压密码申请机制
3. IP访问控制和白名单
4. 访问频率限制和防刷机制
5. 敏感文件标记和监控
6. 文件病毒扫描
7. SQL注入和XSS防护
8. CSRF攻击防护
9. 文件上传安全检查
10. 数据传输加密

请提供完整的安全防护代码和配置。
```

### 提示词6.2: 性能优化
```
请帮我进行系统性能优化。要求：
1. 数据库查询优化和索引优化
2. Redis缓存策略优化
3. 文件传输性能优化
4. 图像处理性能优化
5. 前端资源压缩和CDN配置
6. 数据库连接池优化
7. 异步任务处理优化
8. 内存使用优化
9. 磁盘I/O优化
10. 网络传输优化

请提供性能优化方案和实现代码。
```

## 阶段7: 部署和测试

### 提示词7.1: Windows部署配置
```
请帮我创建Windows部署方案。要求：
1. Python环境自动配置脚本
2. MySQL数据库安装和配置
3. Redis服务安装和配置
4. 系统服务注册脚本
5. 防火墙规则自动配置
6. 启动脚本和停止脚本
7. 自动备份脚本
8. 系统监控脚本
9. 日志轮转配置
10. 一键部署脚本

请提供完整的部署文档和脚本。
```

### 提示词7.2: 系统测试
```
请帮我创建系统测试方案。要求：
1. 单元测试用例（覆盖率>80%）
2. 集成测试用例
3. 性能测试基准
4. 安全测试检查
5. 用户界面测试
6. 兼容性测试
7. 压力测试和负载测试
8. 数据备份恢复测试
9. 故障恢复测试
10. 自动化测试脚本

请提供完整的测试代码和测试报告模板。
```

## 关联性说明

### UI原稿 ↔ 开发文档
- UI原稿中的界面元素对应开发文档中的功能模块
- 权限标识对应权限管理系统设计
- 搜索引擎切换对应双搜索引擎架构

### 开发文档 ↔ 开发提示词
- 每个开发阶段对应文档中的具体功能模块
- 数据库设计对应阶段1的数据库创建提示词
- 权限系统对应阶段2的权限管理提示词

### 提示词内部关联
- 阶段1的基础架构为后续阶段提供基础
- 阶段2的认证系统为文件操作提供安全保障
- 阶段4的搜索引擎依赖阶段3的文件管理
- 阶段5的监控系统贯穿所有功能模块

### 技术栈一致性
- 所有提示词都基于Python+FastAPI+MySQL技术栈
- 前端统一使用原生HTML/CSS/JavaScript
- 部署方案统一针对Windows环境
- 安全机制贯穿整个系统架构
