<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件共享系统测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #666;
            margin-bottom: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.loading {
            background: #fff3cd;
            color: #856404;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .response {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗂️ 企业级文件共享系统测试</h1>
        
        <div class="test-section">
            <h3>🔗 后端连接测试</h3>
            <div id="backend-status" class="status loading">正在检查后端连接...</div>
            <button onclick="testBackend()">重新测试</button>
            <div id="backend-response" class="response" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🌐 API端点测试</h3>
            <button onclick="testEndpoint('/')">测试根路径</button>
            <button onclick="testEndpoint('/health')">健康检查</button>
            <button onclick="testEndpoint('/test')">中文测试</button>
            <div id="api-response" class="response" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 系统信息</h3>
            <div id="system-info">
                <p><strong>前端地址:</strong> <span id="frontend-url"></span></p>
                <p><strong>后端地址:</strong> http://localhost:8000</p>
                <p><strong>API文档:</strong> <a href="http://localhost:8000/docs" target="_blank">http://localhost:8000/docs</a></p>
                <p><strong>测试时间:</strong> <span id="test-time"></span></p>
            </div>
        </div>
    </div>

    <script>
        // 设置页面信息
        document.getElementById('frontend-url').textContent = window.location.href;
        document.getElementById('test-time').textContent = new Date().toLocaleString();
        
        const BACKEND_URL = 'http://localhost:8000';
        
        async function testBackend() {
            const statusEl = document.getElementById('backend-status');
            const responseEl = document.getElementById('backend-response');
            
            statusEl.className = 'status loading';
            statusEl.textContent = '正在测试后端连接...';
            responseEl.style.display = 'none';
            
            try {
                const response = await fetch(BACKEND_URL + '/');
                const data = await response.json();
                
                if (response.ok) {
                    statusEl.className = 'status success';
                    statusEl.textContent = '✅ 后端连接成功';
                    responseEl.textContent = JSON.stringify(data, null, 2);
                    responseEl.style.display = 'block';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 后端连接失败: ' + error.message;
                responseEl.textContent = '错误详情: ' + error.toString();
                responseEl.style.display = 'block';
            }
        }
        
        async function testEndpoint(endpoint) {
            const responseEl = document.getElementById('api-response');
            responseEl.style.display = 'block';
            responseEl.textContent = `正在测试 ${endpoint}...`;
            
            try {
                const response = await fetch(BACKEND_URL + endpoint);
                const data = await response.json();
                
                responseEl.textContent = `端点: ${endpoint}\n状态: ${response.status}\n响应:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                responseEl.textContent = `端点: ${endpoint}\n错误: ${error.message}`;
            }
        }
        
        // 页面加载时自动测试
        window.addEventListener('load', () => {
            setTimeout(testBackend, 1000);
        });
    </script>
</body>
</html>
