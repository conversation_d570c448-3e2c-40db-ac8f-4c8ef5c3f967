// 认证相关功能

// 显示登录模态框
function showLoginModal() {
    const modal = createModal('登录', `
        <form id="login-form">
            <div class="form-group">
                <label class="form-label">用户名</label>
                <input type="text" id="login-username" class="form-input" required>
            </div>
            <div class="form-group">
                <label class="form-label">密码</label>
                <input type="password" id="login-password" class="form-input" required>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="login-remember"> 记住登录状态
                </label>
            </div>
        </form>
    `, [
        {
            text: '取消',
            class: 'btn-secondary',
            onclick: () => closeModal()
        },
        {
            text: '登录',
            class: 'btn-primary',
            onclick: () => handleLogin()
        }
    ]);
    
    showModal(modal);
    
    // 焦点到用户名输入框
    setTimeout(() => {
        document.getElementById('login-username').focus();
    }, 100);
    
    // 回车提交
    document.getElementById('login-form').addEventListener('submit', (e) => {
        e.preventDefault();
        handleLogin();
    });
}

// 处理登录
async function handleLogin() {
    const username = document.getElementById('login-username').value.trim();
    const password = document.getElementById('login-password').value;
    const rememberMe = document.getElementById('login-remember').checked;
    
    if (!username || !password) {
        app.showNotification('请输入用户名和密码', 'error');
        return;
    }
    
    try {
        // 显示加载状态
        const loginBtn = document.querySelector('.modal-footer .btn-primary');
        const originalText = loginBtn.textContent;
        loginBtn.textContent = '登录中...';
        loginBtn.disabled = true;
        
        const response = await api.login(username, password, rememberMe);
        
        // 保存令牌
        api.setToken(response.access_token);
        
        // 保存刷新令牌
        if (response.refresh_token) {
            localStorage.setItem('refresh_token', response.refresh_token);
        }
        
        // 更新用户信息
        app.updateUserInfo(response.user_info);
        
        // 关闭模态框
        closeModal();
        
        // 重新加载数据
        await app.loadInitialData();
        
        app.showNotification('登录成功', 'success');
        
    } catch (error) {
        console.error('登录失败:', error);
        app.showNotification(error.message || '登录失败', 'error');
        
        // 恢复按钮状态
        const loginBtn = document.querySelector('.modal-footer .btn-primary');
        loginBtn.textContent = originalText;
        loginBtn.disabled = false;
    }
}

// 显示注册模态框
function showRegisterModal() {
    const modal = createModal('注册账户', `
        <form id="register-form">
            <div class="form-group">
                <label class="form-label">用户名</label>
                <input type="text" id="register-username" class="form-input" required>
            </div>
            <div class="form-group">
                <label class="form-label">邮箱</label>
                <input type="email" id="register-email" class="form-input" required>
            </div>
            <div class="form-group">
                <label class="form-label">密码</label>
                <input type="password" id="register-password" class="form-input" required>
            </div>
            <div class="form-group">
                <label class="form-label">确认密码</label>
                <input type="password" id="register-confirm-password" class="form-input" required>
            </div>
            <div class="form-group">
                <label class="form-label">真实姓名</label>
                <input type="text" id="register-fullname" class="form-input">
            </div>
            <div class="form-group">
                <label class="form-label">部门</label>
                <input type="text" id="register-department" class="form-input">
            </div>
        </form>
    `, [
        {
            text: '取消',
            class: 'btn-secondary',
            onclick: () => closeModal()
        },
        {
            text: '注册',
            class: 'btn-primary',
            onclick: () => handleRegister()
        }
    ]);
    
    showModal(modal);
    
    // 回车提交
    document.getElementById('register-form').addEventListener('submit', (e) => {
        e.preventDefault();
        handleRegister();
    });
}

// 处理注册
async function handleRegister() {
    const username = document.getElementById('register-username').value.trim();
    const email = document.getElementById('register-email').value.trim();
    const password = document.getElementById('register-password').value;
    const confirmPassword = document.getElementById('register-confirm-password').value;
    const fullName = document.getElementById('register-fullname').value.trim();
    const department = document.getElementById('register-department').value.trim();
    
    // 验证输入
    if (!username || !email || !password) {
        app.showNotification('请填写必填字段', 'error');
        return;
    }
    
    if (password !== confirmPassword) {
        app.showNotification('两次输入的密码不一致', 'error');
        return;
    }
    
    if (password.length < 6) {
        app.showNotification('密码长度至少6位', 'error');
        return;
    }
    
    try {
        // 显示加载状态
        const registerBtn = document.querySelector('.modal-footer .btn-primary');
        const originalText = registerBtn.textContent;
        registerBtn.textContent = '注册中...';
        registerBtn.disabled = true;
        
        const userData = {
            username,
            email,
            password,
            full_name: fullName,
            department
        };
        
        await api.register(userData);
        
        // 关闭模态框
        closeModal();
        
        app.showNotification('注册成功，请登录', 'success');
        
        // 显示登录模态框
        setTimeout(() => {
            showLoginModal();
            // 预填用户名
            setTimeout(() => {
                document.getElementById('login-username').value = username;
                document.getElementById('login-password').focus();
            }, 100);
        }, 500);
        
    } catch (error) {
        console.error('注册失败:', error);
        app.showNotification(error.message || '注册失败', 'error');
        
        // 恢复按钮状态
        const registerBtn = document.querySelector('.modal-footer .btn-primary');
        registerBtn.textContent = originalText;
        registerBtn.disabled = false;
    }
}

// 登出
async function logout() {
    try {
        await api.logout();
    } catch (error) {
        console.error('登出请求失败:', error);
    }
    
    // 清除本地存储
    api.setToken(null);
    localStorage.removeItem('refresh_token');
    
    // 重置用户界面
    const userInfo = document.getElementById('user-info');
    const userName = userInfo.querySelector('.user-name');
    const loginBtn = document.getElementById('login-btn');
    const profileBtn = document.getElementById('profile-btn');
    const logoutBtn = document.getElementById('logout-btn');
    
    userName.textContent = '未登录';
    loginBtn.style.display = 'block';
    profileBtn.style.display = 'none';
    logoutBtn.style.display = 'none';
    
    // 清空文件列表
    document.getElementById('file-grid').innerHTML = '';
    document.getElementById('empty-state').style.display = 'flex';
    
    app.showNotification('已退出登录', 'info');
    
    // 显示登录模态框
    setTimeout(() => {
        showLoginModal();
    }, 1000);
}

// 切换用户下拉菜单
function toggleUserDropdown() {
    const dropdown = document.getElementById('user-dropdown');
    const isVisible = dropdown.style.display === 'block';
    
    if (isVisible) {
        dropdown.style.display = 'none';
    } else {
        dropdown.style.display = 'block';
    }
}

// 显示个人资料模态框
function showProfileModal() {
    // TODO: 实现个人资料编辑功能
    app.showNotification('个人资料功能开发中', 'info');
}

// 自动刷新令牌
async function autoRefreshToken() {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
        return false;
    }
    
    try {
        const response = await api.refreshToken(refreshToken);
        api.setToken(response.access_token);
        
        if (response.refresh_token) {
            localStorage.setItem('refresh_token', response.refresh_token);
        }
        
        return true;
    } catch (error) {
        console.error('刷新令牌失败:', error);
        // 清除无效的令牌
        api.setToken(null);
        localStorage.removeItem('refresh_token');
        return false;
    }
}

// 检查令牌是否即将过期并自动刷新
function startTokenRefreshTimer() {
    // 每25分钟检查一次（令牌30分钟过期）
    setInterval(async () => {
        if (api.token) {
            const success = await autoRefreshToken();
            if (!success) {
                // 刷新失败，显示登录模态框
                showLoginModal();
            }
        }
    }, 25 * 60 * 1000);
}

// 启动令牌刷新定时器
startTokenRefreshTimer();

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        showLoginModal,
        showRegisterModal,
        handleLogin,
        handleRegister,
        logout,
        toggleUserDropdown,
        showProfileModal,
        autoRefreshToken,
        startTokenRefreshTimer
    };
}
