<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业级文件共享系统 - 开发提示词指南</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 30px rgba(0,0,0,0.2);
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .header h1 {
            font-size: 2.8em;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }
        
        .header p {
            font-size: 1.3em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .phase-nav {
            background: #34495e;
            padding: 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .phase-nav ul {
            list-style: none;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .phase-nav li {
            flex: 1;
            min-width: 180px;
            max-width: 200px;
        }
        
        .phase-nav a {
            display: block;
            padding: 15px 20px;
            color: white;
            text-decoration: none;
            border-right: 1px solid #2c3e50;
            transition: all 0.3s;
            text-align: center;
            font-weight: 500;
        }
        
        .phase-nav a:hover {
            background: #2c3e50;
            transform: translateY(-2px);
        }
        
        .content {
            padding: 40px;
            background: #f8f9fa;
        }
        
        .phase-section {
            margin-bottom: 60px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .phase-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .phase-header h2 {
            font-size: 2.2em;
            margin-bottom: 10px;
        }
        
        .phase-header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .phase-content {
            padding: 40px;
        }
        
        .prompt-card {
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            margin-bottom: 30px;
            overflow: hidden;
            transition: all 0.3s;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .prompt-card:hover {
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .prompt-header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            font-weight: bold;
            font-size: 1.2em;
        }
        
        .prompt-body {
            padding: 25px;
        }
        
        .prompt-text {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            white-space: pre-wrap;
            overflow-x: auto;
            border-left: 4px solid #3498db;
        }
        
        .requirements {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .requirements h4 {
            color: #2d5a2d;
            margin-bottom: 10px;
        }
        
        .requirements ul {
            margin: 10px 0;
            padding-left: 25px;
        }
        
        .requirements li {
            margin: 8px 0;
            color: #2d5a2d;
        }
        
        .tech-highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .warning-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            float: right;
            margin-top: -10px;
            transition: background 0.3s;
        }
        
        .copy-btn:hover {
            background: #218838;
        }
        
        .relationship-diagram {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }
        
        .relationship-item {
            display: inline-block;
            background: white;
            padding: 15px 20px;
            margin: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 40px;
        }
        
        @media (max-width: 768px) {
            .phase-nav ul {
                flex-direction: column;
            }
            
            .phase-nav a {
                border-right: none;
                border-bottom: 1px solid #2c3e50;
            }
            
            .content {
                padding: 20px;
            }
            
            .phase-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1>🚀 企业级文件共享系统</h1>
            <p>开发提示词指南 - 七个阶段完整开发流程</p>
        </header>

        <!-- 阶段导航 -->
        <nav class="phase-nav">
            <ul>
                <li><a href="#phase1">阶段1: 基础架构</a></li>
                <li><a href="#phase2">阶段2: 用户认证</a></li>
                <li><a href="#phase3">阶段3: 文件管理</a></li>
                <li><a href="#phase4">阶段4: 双搜索引擎</a></li>
                <li><a href="#phase5">阶段5: 监控管理</a></li>
                <li><a href="#phase6">阶段6: 安全优化</a></li>
                <li><a href="#phase7">阶段7: 部署测试</a></li>
            </ul>
        </nav>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 阶段1: 基础架构 -->
            <section id="phase1" class="phase-section">
                <div class="phase-header">
                    <h2>🏗️ 阶段1: 项目初始化和基础架构搭建</h2>
                    <p>建立项目基础框架，配置开发环境和数据库</p>
                </div>
                <div class="phase-content">
                    <div class="prompt-card">
                        <div class="prompt-header">
                            📁 提示词1.1: 项目结构创建
                            <button class="copy-btn" onclick="copyPrompt(this)">复制</button>
                        </div>
                        <div class="prompt-body">
                            <div class="requirements">
                                <h4>🎯 核心要求：</h4>
                                <ul>
                                    <li>使用Python + FastAPI作为后端框架</li>
                                    <li>使用MySQL作为主数据库，Redis作为缓存</li>
                                    <li>前端使用原生HTML/CSS/JavaScript</li>
                                    <li>Windows环境直接部署，不使用Docker</li>
                                </ul>
                            </div>
                            <div class="prompt-text">请帮我创建一个企业级文件共享系统的项目结构。要求：
1. 使用Python + FastAPI作为后端框架
2. 使用MySQL作为主数据库，Redis作为缓存
3. 前端使用原生HTML/CSS/JavaScript
4. 创建完整的目录结构，包括backend、frontend、storage、logs、config等目录
5. 生成requirements.txt文件，包含所有必要的Python依赖
6. 创建基础的配置文件和启动脚本
7. 确保项目可以在Windows环境下直接部署，不使用Docker

请按照以下目录结构创建：
- backend/ (后端代码)
- frontend/ (前端代码)  
- storage/ (文件存储)
- logs/ (日志文件)
- config/ (配置文件)
- docs/ (文档)</div>
                        </div>
                    </div>

                    <div class="prompt-card">
                        <div class="prompt-header">
                            🗄️ 提示词1.2: 数据库设计和初始化
                            <button class="copy-btn" onclick="copyPrompt(this)">复制</button>
                        </div>
                        <div class="prompt-body">
                            <div class="tech-highlight">
                                <strong>💡 设计重点：</strong> 支持细粒度权限控制、操作日志记录、文件版本管理
                            </div>
                            <div class="prompt-text">请帮我设计并创建企业级文件共享系统的数据库结构。要求：
1. 创建用户表(users)，包含用户信息、角色、权限、登录状态等字段
2. 创建文件表(files)，包含文件信息、路径、大小、类型、上传者等字段
3. 创建权限表(permissions)，支持细粒度权限控制
4. 创建操作日志表(activity_logs)，记录所有用户操作
5. 创建文件夹表(folders)，支持目录结构管理
6. 创建系统配置表(system_config)，存储系统设置
7. 添加必要的索引以优化查询性能
8. 创建数据库初始化脚本和示例数据
9. 考虑数据库的扩展性和性能优化

请提供完整的SQL建表语句和Python数据库连接代码。</div>
                        </div>
                    </div>

                    <div class="prompt-card">
                        <div class="prompt-header">
                            ⚡ 提示词1.3: FastAPI基础框架搭建
                            <button class="copy-btn" onclick="copyPrompt(this)">复制</button>
                        </div>
                        <div class="prompt-body">
                            <div class="warning-box">
                                <strong>⚠️ 注意：</strong> 确保代码结构清晰，易于维护和扩展，支持高并发访问
                            </div>
                            <div class="prompt-text">请帮我搭建FastAPI基础框架。要求：
1. 创建main.py作为应用入口
2. 配置数据库连接池(MySQL + Redis)
3. 设置CORS中间件支持跨域访问
4. 创建用户认证中间件(JWT Token)
5. 设置请求日志中间件
6. 创建基础的路由结构：
   - /auth (认证相关)
   - /files (文件操作)
   - /users (用户管理)
   - /admin (管理功能)
   - /search (搜索功能)
7. 创建统一的响应格式和错误处理
8. 配置静态文件服务
9. 添加API文档自动生成
10. 确保代码结构清晰，易于维护和扩展

请提供完整的代码实现和配置说明。</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 阶段2: 用户认证 -->
            <section id="phase2" class="phase-section">
                <div class="phase-header">
                    <h2>👥 阶段2: 用户认证和权限管理</h2>
                    <p>实现用户登录认证、权限控制和用户管理功能</p>
                </div>
                <div class="phase-content">
                    <div class="prompt-card">
                        <div class="prompt-header">
                            🔐 提示词2.1: 用户认证系统
                            <button class="copy-btn" onclick="copyPrompt(this)">复制</button>
                        </div>
                        <div class="prompt-body">
                            <div class="info-box">
                                <strong>🔒 安全要求：</strong> 密码加密存储、登录失败锁定、会话管理
                            </div>
                            <div class="prompt-text">请帮我实现用户认证系统。要求：
1. 用户注册功能（管理员可控制开启/关闭）
2. 用户登录功能，支持用户名/邮箱登录
3. JWT Token生成和验证
4. 密码加密存储（使用bcrypt）
5. 登录失败次数限制和账户锁定机制
6. 记住登录状态功能
7. 用户会话管理（Redis存储）
8. 登录日志记录
9. 密码强度验证
10. 支持管理员重置用户密码

请提供完整的认证相关API接口和前端登录页面。</div>
                        </div>
                    </div>

                    <div class="prompt-card">
                        <div class="prompt-header">
                            🛡️ 提示词2.2: 权限管理系统
                            <button class="copy-btn" onclick="copyPrompt(this)">复制</button>
                        </div>
                        <div class="prompt-body">
                            <div class="requirements">
                                <h4>🎯 权限层级：</h4>
                                <ul>
                                    <li>角色权限：管理员、普通用户、只读用户</li>
                                    <li>文件权限：读取、写入、删除、下载、上传</li>
                                    <li>网络权限：内网访问、外网访问</li>
                                </ul>
                            </div>
                            <div class="prompt-text">请帮我实现细粒度的权限管理系统。要求：
1. 角色权限系统：管理员、普通用户、只读用户
2. 文件级权限控制：读取、写入、删除、下载、上传
3. 文件夹级权限继承机制
4. 用户组管理功能
5. 权限检查装饰器和中间件
6. 动态权限分配和回收
7. 权限变更日志记录
8. 批量权限设置功能
9. 权限模板功能
10. 内外网访问权限控制

请提供权限检查的核心代码和管理界面。</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 阶段3: 文件管理 -->
            <section id="phase3" class="phase-section">
                <div class="phase-header">
                    <h2>📁 阶段3: 文件管理核心功能</h2>
                    <p>实现文件上传、下载、预览和管理功能</p>
                </div>
                <div class="phase-content">
                    <div class="prompt-card">
                        <div class="prompt-header">
                            📤 提示词3.1: 文件上传功能
                            <button class="copy-btn" onclick="copyPrompt(this)">复制</button>
                        </div>
                        <div class="prompt-body">
                            <div class="tech-highlight">
                                <strong>📋 支持格式：</strong> JPG, PNG, TIF, PSD, AI, EPS等多种图像格式
                            </div>
                            <div class="prompt-text">请帮我实现文件上传功能。要求：
1. 支持单文件和多文件上传
2. 支持拖拽上传
3. 大文件分片上传和断点续传
4. 上传进度显示
5. 文件类型和大小限制
6. 文件重名处理策略
7. 上传文件病毒扫描
8. 缩略图自动生成（图片文件）
9. 文件MD5校验防重复
10. 上传权限检查
11. 支持的文件格式：JPG, PNG, TIF, PSD, AI, EPS等

请提供后端上传API和前端上传组件。</div>
                        </div>
                    </div>

                    <div class="prompt-card">
                        <div class="prompt-header">
                            📥 提示词3.2: 文件下载和预览
                            <button class="copy-btn" onclick="copyPrompt(this)">复制</button>
                        </div>
                        <div class="prompt-body">
                            <div class="warning-box">
                                <strong>🔐 安全要求：</strong> 下载加密、权限验证、速度限制
                            </div>
                            <div class="prompt-text">请帮我实现文件下载和预览功能。要求：
1. 单文件下载
2. 批量文件下载（ZIP打包）
3. 文件夹打包下载
4. 下载权限验证
5. 下载次数统计和限制
6. 下载速度限制（防止占用过多带宽）
7. 文件预览功能（图片、PDF、文本）
8. 缩略图展示（超大、大、中等图标模式）
9. 文件加密下载（可配置加密次数）
10. 下载密码申请机制
11. 下载日志记录

请提供完整的下载API和前端预览界面。</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 阶段4: 双搜索引擎 -->
            <section id="phase4" class="phase-section">
                <div class="phase-header">
                    <h2>🔍 阶段4: 双搜索引擎实现</h2>
                    <p>实现文件名搜索和图像识别搜索功能</p>
                </div>
                <div class="phase-content">
                    <div class="prompt-card">
                        <div class="prompt-header">
                            ⚡ 提示词4.1: 文件名搜索引擎
                            <button class="copy-btn" onclick="copyPrompt(this)">复制</button>
                        </div>
                        <div class="prompt-body">
                            <div class="info-box">
                                <strong>🚀 性能要求：</strong> 搜索速度达到Everything级别，支持中文分词
                            </div>
                            <div class="prompt-text">请帮我实现高性能的文件名搜索引擎。要求：
1. 基于MySQL全文索引的快速搜索
2. 支持模糊搜索、精确搜索、正则表达式搜索
3. 搜索结果按相关度排序
4. 搜索历史记录和热门搜索
5. 搜索建议和自动补全
6. 高级搜索功能（文件类型、大小、时间范围）
7. 搜索结果分页和无限滚动
8. 搜索性能优化（缓存、索引）
9. 搜索统计和分析
10. 支持中文分词搜索

请提供搜索API接口和前端搜索组件，确保搜索速度达到Everything级别。</div>
                        </div>
                    </div>

                    <div class="prompt-card">
                        <div class="prompt-header">
                            🖼️ 提示词4.2: 图像识别搜索引擎
                            <button class="copy-btn" onclick="copyPrompt(this)">复制</button>
                        </div>
                        <div class="prompt-body">
                            <div class="tech-highlight">
                                <strong>🔬 技术核心：</strong> OpenCV图像处理 + 特征向量匹配算法
                            </div>
                            <div class="prompt-text">请帮我实现图像识别搜索引擎。要求：
1. 使用OpenCV进行图像特征提取
2. 支持多种图像格式：JPG, PNG, TIF, PSD, AI, EPS
3. 图像相似度计算和匹配算法
4. 特征向量存储和索引优化
5. 以图搜图功能
6. 图像内容识别（物体、场景、颜色）
7. 图像质量评估
8. 批量图像特征提取
9. 搜索结果相似度排序
10. 图像搜索性能优化

请提供图像处理核心算法和搜索接口，确保识图搜索快速准确。</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 阶段5: 监控管理 -->
            <section id="phase5" class="phase-section">
                <div class="phase-header">
                    <h2>📊 阶段5: 系统监控和管理</h2>
                    <p>实现实时监控、日志管理和管理员控制面板</p>
                </div>
                <div class="phase-content">
                    <div class="prompt-card">
                        <div class="prompt-header">
                            📈 提示词5.1: 实时监控系统
                            <button class="copy-btn" onclick="copyPrompt(this)">复制</button>
                        </div>
                        <div class="prompt-body">
                            <div class="info-box">
                                <strong>📊 监控范围：</strong> 用户行为、系统资源、异常检测、性能指标
                            </div>
                            <div class="prompt-text">请帮我实现系统实时监控功能。要求：
1. 在线用户实时统计和显示
2. 系统资源监控（CPU、内存、磁盘、网络）
3. 文件操作实时监控
4. 异常行为检测和告警
5. 性能指标实时展示
6. 系统日志实时查看
7. 数据库连接池监控
8. 缓存使用情况监控
9. 文件存储空间监控
10. 网络流量监控

请提供监控数据收集API和实时监控仪表板。</div>
                        </div>
                    </div>

                    <div class="prompt-card">
                        <div class="prompt-header">
                            📋 提示词5.2: 日志和统计系统
                            <button class="copy-btn" onclick="copyPrompt(this)">复制</button>
                        </div>
                        <div class="prompt-body">
                            <div class="warning-box">
                                <strong>⚠️ 重点监控：</strong> 敏感操作重点标注，删除修改严格记录
                            </div>
                            <div class="prompt-text">请帮我实现完整的日志和统计系统。要求：
1. 用户行为日志记录（登录、搜索、下载、上传）
2. 系统操作日志记录
3. 错误日志记录和分析
4. 日志分级和轮转
5. 用户行为统计分析
6. 文件访问热度统计
7. 系统使用情况报表
8. 日志搜索和筛选功能
9. 日志导出功能
10. 敏感操作重点标注和警告

请提供日志记录框架和统计分析界面。</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 阶段6: 安全优化 -->
            <section id="phase6" class="phase-section">
                <div class="phase-header">
                    <h2>🔒 阶段6: 安全和优化</h2>
                    <p>实现安全机制和系统性能优化</p>
                </div>
                <div class="phase-content">
                    <div class="prompt-card">
                        <div class="prompt-header">
                            🛡️ 提示词6.1: 安全机制实现
                            <button class="copy-btn" onclick="copyPrompt(this)">复制</button>
                        </div>
                        <div class="prompt-body">
                            <div class="requirements">
                                <h4>🔐 安全特性：</h4>
                                <ul>
                                    <li>文件下载加密（可配置加密次数）</li>
                                    <li>解压密码申请机制</li>
                                    <li>敏感文件监控和警告</li>
                                    <li>访问频率限制和防刷</li>
                                </ul>
                            </div>
                            <div class="prompt-text">请帮我实现系统安全机制。要求：
1. 文件下载加密功能
2. 解压密码申请机制
3. IP访问控制和白名单
4. 访问频率限制和防刷机制
5. 敏感文件标记和监控
6. 文件病毒扫描
7. SQL注入和XSS防护
8. CSRF攻击防护
9. 文件上传安全检查
10. 数据传输加密

请提供完整的安全防护代码和配置。</div>
                        </div>
                    </div>

                    <div class="prompt-card">
                        <div class="prompt-header">
                            ⚡ 提示词6.2: 性能优化
                            <button class="copy-btn" onclick="copyPrompt(this)">复制</button>
                        </div>
                        <div class="prompt-body">
                            <div class="tech-highlight">
                                <strong>🚀 优化目标：</strong> 快准稳简洁，确保系统高性能运行
                            </div>
                            <div class="prompt-text">请帮我进行系统性能优化。要求：
1. 数据库查询优化和索引优化
2. Redis缓存策略优化
3. 文件传输性能优化
4. 图像处理性能优化
5. 前端资源压缩和CDN配置
6. 数据库连接池优化
7. 异步任务处理优化
8. 内存使用优化
9. 磁盘I/O优化
10. 网络传输优化

请提供性能优化方案和实现代码。</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 阶段7: 部署测试 -->
            <section id="phase7" class="phase-section">
                <div class="phase-header">
                    <h2>🚀 阶段7: 部署和测试</h2>
                    <p>Windows部署配置和系统测试</p>
                </div>
                <div class="phase-content">
                    <div class="prompt-card">
                        <div class="prompt-header">
                            💻 提示词7.1: Windows部署配置
                            <button class="copy-btn" onclick="copyPrompt(this)">复制</button>
                        </div>
                        <div class="prompt-body">
                            <div class="info-box">
                                <strong>🎯 部署目标：</strong> 一键部署，自动配置，简单易用
                            </div>
                            <div class="prompt-text">请帮我创建Windows部署方案。要求：
1. Python环境自动配置脚本
2. MySQL数据库安装和配置
3. Redis服务安装和配置
4. 系统服务注册脚本
5. 防火墙规则自动配置
6. 启动脚本和停止脚本
7. 自动备份脚本
8. 系统监控脚本
9. 日志轮转配置
10. 一键部署脚本

请提供完整的部署文档和脚本。</div>
                        </div>
                    </div>

                    <div class="prompt-card">
                        <div class="prompt-header">
                            🧪 提示词7.2: 系统测试
                            <button class="copy-btn" onclick="copyPrompt(this)">复制</button>
                        </div>
                        <div class="prompt-body">
                            <div class="warning-box">
                                <strong>⚠️ 测试重点：</strong> 兼容性测试，确保新版系统不影响使用
                            </div>
                            <div class="prompt-text">请帮我创建系统测试方案。要求：
1. 单元测试用例（覆盖率>80%）
2. 集成测试用例
3. 性能测试基准
4. 安全测试检查
5. 用户界面测试
6. 兼容性测试
7. 压力测试和负载测试
8. 数据备份恢复测试
9. 故障恢复测试
10. 自动化测试脚本

请提供完整的测试代码和测试报告模板。</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 关联性说明 -->
            <div class="relationship-diagram">
                <h3>📊 各阶段关联性说明</h3>
                <div class="relationship-item">UI原稿 ↔ 开发文档<br><small>界面元素对应功能模块</small></div>
                <div class="relationship-item">开发文档 ↔ 提示词<br><small>功能设计对应开发步骤</small></div>
                <div class="relationship-item">阶段间依赖<br><small>基础架构支撑上层功能</small></div>
                <div class="relationship-item">技术栈一致<br><small>Python+FastAPI+MySQL</small></div>
                <div class="relationship-item">安全贯穿<br><small>安全机制贯穿所有模块</small></div>
                <div class="relationship-item">Windows部署<br><small>统一针对Windows环境</small></div>
            </div>
        </div>

        <!-- 页脚 -->
        <footer class="footer">
            <p>&copy; 2024 企业级文件共享系统开发提示词指南</p>
            <p>技术栈：Python + FastAPI + MySQL + Redis | 部署：Windows直接部署</p>
        </footer>
    </div>

    <script>
        // 复制提示词功能
        function copyPrompt(button) {
            const promptText = button.parentElement.nextElementSibling.querySelector('.prompt-text');
            const text = promptText.textContent;
            
            navigator.clipboard.writeText(text).then(function() {
                button.textContent = '已复制!';
                button.style.background = '#28a745';
                setTimeout(function() {
                    button.textContent = '复制';
                    button.style.background = '#28a745';
                }, 2000);
            });
        }
        
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // 导航高亮
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.phase-section');
            const navLinks = document.querySelectorAll('.phase-nav a');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                if (scrollY >= (sectionTop - 200)) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
