# -*- coding: utf-8 -*-
"""
User Models
用户模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Enum, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.database import Base


class UserRole(enum.Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    USER = "user"
    READONLY = "readonly"


class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    username = Column(String(50), unique=True, index=True, nullable=False, comment="用户名")
    email = Column(String(100), unique=True, index=True, nullable=True, comment="邮箱")
    password_hash = Column(String(255), nullable=False, comment="密码哈希")
    
    # 用户信息
    full_name = Column(String(100), nullable=True, comment="真实姓名")
    phone = Column(String(20), nullable=True, comment="电话号码")
    department = Column(String(100), nullable=True, comment="部门")
    
    # 角色和权限
    role = Column(Enum(UserRole), default=UserRole.USER, nullable=False, comment="用户角色")
    group_id = Column(Integer, nullable=True, comment="用户组ID")
    
    # 状态信息
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    is_verified = Column(Boolean, default=False, nullable=False, comment="是否验证")
    
    # 登录安全
    login_attempts = Column(Integer, default=0, nullable=False, comment="登录尝试次数")
    locked_until = Column(DateTime, nullable=True, comment="锁定到期时间")
    last_login = Column(DateTime, nullable=True, comment="最后登录时间")
    last_login_ip = Column(String(45), nullable=True, comment="最后登录IP")
    
    # 网络权限
    internal_access = Column(Boolean, default=True, nullable=False, comment="内网访问权限")
    external_access = Column(Boolean, default=False, nullable=False, comment="外网访问权限")
    ip_whitelist = Column(Text, nullable=True, comment="IP白名单，JSON格式")
    
    # 下载限制
    max_download_files = Column(Integer, default=10, nullable=False, comment="最大下载文件数")
    max_download_size_mb = Column(Integer, default=500, nullable=False, comment="最大下载大小MB")
    download_speed_limit_kb = Column(Integer, default=1024, nullable=False, comment="下载速度限制KB/s")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    
    # 备注
    notes = Column(Text, nullable=True, comment="备注信息")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role.value}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "full_name": self.full_name,
            "phone": self.phone,
            "department": self.department,
            "role": self.role.value,
            "group_id": self.group_id,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "internal_access": self.internal_access,
            "external_access": self.external_access,
            "max_download_files": self.max_download_files,
            "max_download_size_mb": self.max_download_size_mb,
            "download_speed_limit_kb": self.download_speed_limit_kb,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    def is_admin(self) -> bool:
        """检查是否为管理员"""
        return self.role == UserRole.ADMIN
    
    def is_readonly(self) -> bool:
        """检查是否为只读用户"""
        return self.role == UserRole.READONLY
    
    def can_access_external(self) -> bool:
        """检查是否可以外网访问"""
        return self.external_access and self.is_active
    
    def is_locked(self) -> bool:
        """检查账户是否被锁定"""
        if self.locked_until is None:
            return False
        from datetime import datetime
        return datetime.now() < self.locked_until


class UserGroup(Base):
    """用户组模型"""
    __tablename__ = "user_groups"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(100), unique=True, nullable=False, comment="组名")
    description = Column(Text, nullable=True, comment="组描述")
    
    # 默认权限
    default_role = Column(Enum(UserRole), default=UserRole.USER, nullable=False, comment="默认角色")
    default_internal_access = Column(Boolean, default=True, nullable=False, comment="默认内网访问")
    default_external_access = Column(Boolean, default=False, nullable=False, comment="默认外网访问")
    
    # 默认限制
    default_max_download_files = Column(Integer, default=10, nullable=False, comment="默认最大下载文件数")
    default_max_download_size_mb = Column(Integer, default=500, nullable=False, comment="默认最大下载大小MB")
    default_download_speed_limit_kb = Column(Integer, default=1024, nullable=False, comment="默认下载速度限制KB/s")
    
    # 状态
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    
    def __repr__(self):
        return f"<UserGroup(id={self.id}, name='{self.name}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "default_role": self.default_role.value,
            "default_internal_access": self.default_internal_access,
            "default_external_access": self.default_external_access,
            "default_max_download_files": self.default_max_download_files,
            "default_max_download_size_mb": self.default_max_download_size_mb,
            "default_download_speed_limit_kb": self.default_download_speed_limit_kb,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
