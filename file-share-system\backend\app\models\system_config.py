"""
系统配置模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base


class SystemConfig(Base):
    """系统配置模型"""
    __tablename__ = "system_configs"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 配置信息
    config_key = Column(String(100), unique=True, nullable=False, index=True, comment="配置键")
    config_value = Column(Text, nullable=True, comment="配置值")
    config_type = Column(String(20), default="string", nullable=False, comment="配置类型")
    
    # 配置分类
    category = Column(String(50), nullable=False, index=True, comment="配置分类")
    subcategory = Column(String(50), nullable=True, comment="配置子分类")
    
    # 配置描述
    display_name = Column(String(100), nullable=False, comment="显示名称")
    description = Column(Text, nullable=True, comment="配置描述")
    
    # 配置约束
    default_value = Column(Text, nullable=True, comment="默认值")
    min_value = Column(Float, nullable=True, comment="最小值")
    max_value = Column(Float, nullable=True, comment="最大值")
    allowed_values = Column(Text, nullable=True, comment="允许的值JSON")
    
    # 配置状态
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    is_readonly = Column(Boolean, default=False, nullable=False, comment="是否只读")
    is_sensitive = Column(Boolean, default=False, nullable=False, comment="是否敏感")
    
    # 更新信息
    updated_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="更新者ID")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    
    # 关联关系
    updater = relationship("User", foreign_keys=[updated_by])
    
    def __repr__(self):
        return f"<SystemConfig(id={self.id}, key='{self.config_key}', value='{self.config_value}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "config_key": self.config_key,
            "config_value": self.config_value,
            "config_type": self.config_type,
            "category": self.category,
            "subcategory": self.subcategory,
            "display_name": self.display_name,
            "description": self.description,
            "default_value": self.default_value,
            "min_value": self.min_value,
            "max_value": self.max_value,
            "allowed_values": self.allowed_values,
            "is_active": self.is_active,
            "is_readonly": self.is_readonly,
            "is_sensitive": self.is_sensitive,
            "updated_by": self.updated_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    def get_typed_value(self):
        """获取类型化的配置值"""
        if self.config_value is None:
            return None
        
        if self.config_type == "boolean":
            return self.config_value.lower() in ("true", "1", "yes", "on")
        elif self.config_type == "integer":
            try:
                return int(self.config_value)
            except ValueError:
                return None
        elif self.config_type == "float":
            try:
                return float(self.config_value)
            except ValueError:
                return None
        elif self.config_type == "json":
            try:
                import json
                return json.loads(self.config_value)
            except (ValueError, TypeError):
                return None
        else:
            return self.config_value


class SystemStats(Base):
    """系统统计模型"""
    __tablename__ = "system_stats"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 统计日期
    stat_date = Column(DateTime, nullable=False, index=True, comment="统计日期")
    stat_type = Column(String(50), nullable=False, index=True, comment="统计类型")
    
    # 用户统计
    total_users = Column(Integer, default=0, comment="总用户数")
    active_users = Column(Integer, default=0, comment="活跃用户数")
    new_users = Column(Integer, default=0, comment="新增用户数")
    online_users = Column(Integer, default=0, comment="在线用户数")
    
    # 文件统计
    total_files = Column(Integer, default=0, comment="总文件数")
    new_files = Column(Integer, default=0, comment="新增文件数")
    deleted_files = Column(Integer, default=0, comment="删除文件数")
    total_size_mb = Column(Float, default=0, comment="总文件大小MB")
    
    # 操作统计
    total_uploads = Column(Integer, default=0, comment="总上传次数")
    total_downloads = Column(Integer, default=0, comment="总下载次数")
    total_searches = Column(Integer, default=0, comment="总搜索次数")
    total_views = Column(Integer, default=0, comment="总查看次数")
    
    # 流量统计
    upload_traffic_mb = Column(Float, default=0, comment="上传流量MB")
    download_traffic_mb = Column(Float, default=0, comment="下载流量MB")
    
    # 系统资源
    avg_cpu_usage = Column(Float, default=0, comment="平均CPU使用率")
    avg_memory_usage = Column(Float, default=0, comment="平均内存使用率")
    avg_disk_usage = Column(Float, default=0, comment="平均磁盘使用率")
    
    # 错误统计
    error_count = Column(Integer, default=0, comment="错误次数")
    warning_count = Column(Integer, default=0, comment="警告次数")
    security_alert_count = Column(Integer, default=0, comment="安全告警次数")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    
    def __repr__(self):
        return f"<SystemStats(id={self.id}, date='{self.stat_date}', type='{self.stat_type}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "stat_date": self.stat_date.isoformat() if self.stat_date else None,
            "stat_type": self.stat_type,
            "total_users": self.total_users,
            "active_users": self.active_users,
            "new_users": self.new_users,
            "online_users": self.online_users,
            "total_files": self.total_files,
            "new_files": self.new_files,
            "deleted_files": self.deleted_files,
            "total_size_mb": self.total_size_mb,
            "total_uploads": self.total_uploads,
            "total_downloads": self.total_downloads,
            "total_searches": self.total_searches,
            "total_views": self.total_views,
            "upload_traffic_mb": self.upload_traffic_mb,
            "download_traffic_mb": self.download_traffic_mb,
            "avg_cpu_usage": self.avg_cpu_usage,
            "avg_memory_usage": self.avg_memory_usage,
            "avg_disk_usage": self.avg_disk_usage,
            "error_count": self.error_count,
            "warning_count": self.warning_count,
            "security_alert_count": self.security_alert_count,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class Notification(Base):
    """通知模型"""
    __tablename__ = "notifications"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 通知信息
    title = Column(String(255), nullable=False, comment="通知标题")
    content = Column(Text, nullable=False, comment="通知内容")
    notification_type = Column(String(50), nullable=False, comment="通知类型")
    
    # 目标用户
    target_user_id = Column(Integer, ForeignKey("users.id"), nullable=True, comment="目标用户ID")
    target_role = Column(String(20), nullable=True, comment="目标角色")
    is_global = Column(Boolean, default=False, comment="是否全局通知")
    
    # 通知状态
    is_read = Column(Boolean, default=False, comment="是否已读")
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 显示设置
    show_popup = Column(Boolean, default=False, comment="是否弹窗显示")
    show_banner = Column(Boolean, default=True, comment="是否横幅显示")
    auto_dismiss = Column(Boolean, default=False, comment="是否自动消失")
    dismiss_after_seconds = Column(Integer, nullable=True, comment="自动消失秒数")
    
    # 优先级
    priority = Column(Integer, default=1, comment="优先级")
    
    # 有效期
    valid_from = Column(DateTime, nullable=True, comment="生效时间")
    valid_until = Column(DateTime, nullable=True, comment="过期时间")
    
    # 创建信息
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False, comment="创建者ID")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    read_at = Column(DateTime, nullable=True, comment="阅读时间")
    
    # 关联关系
    target_user = relationship("User", foreign_keys=[target_user_id])
    creator = relationship("User", foreign_keys=[created_by])
    
    def __repr__(self):
        return f"<Notification(id={self.id}, title='{self.title}', type='{self.notification_type}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "title": self.title,
            "content": self.content,
            "notification_type": self.notification_type,
            "target_user_id": self.target_user_id,
            "target_role": self.target_role,
            "is_global": self.is_global,
            "is_read": self.is_read,
            "is_active": self.is_active,
            "show_popup": self.show_popup,
            "show_banner": self.show_banner,
            "auto_dismiss": self.auto_dismiss,
            "dismiss_after_seconds": self.dismiss_after_seconds,
            "priority": self.priority,
            "valid_from": self.valid_from.isoformat() if self.valid_from else None,
            "valid_until": self.valid_until.isoformat() if self.valid_until else None,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "read_at": self.read_at.isoformat() if self.read_at else None
        }
    
    def is_valid(self) -> bool:
        """检查通知是否有效"""
        if not self.is_active:
            return False
        
        from datetime import datetime
        now = datetime.now()
        
        if self.valid_from and now < self.valid_from:
            return False
        
        if self.valid_until and now > self.valid_until:
            return False
        
        return True
