"""
日志中间件
"""
import time
import json
from typing import Optional
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger

from app.database import get_db
from app.models.activity_log import ActivityLog, ActionType, TargetType, LogLevel


class LoggingMiddleware(BaseHTTPMiddleware):
    """日志中间件"""
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        start_time = time.time()
        
        # 获取请求信息
        method = request.method
        url = str(request.url)
        path = request.url.path
        client_ip = self.get_client_ip(request)
        user_agent = request.headers.get("User-Agent", "")
        
        # 获取用户信息
        user_id = getattr(request.state, "user_id", None)
        username = getattr(request.state, "user", None)
        username = username.username if username else "anonymous"
        
        # 记录请求开始
        logger.info(f"🔄 {method} {path} - IP: {client_ip} - User: {username}")
        
        # 处理请求
        try:
            response = await call_next(request)
            process_time = time.time() - start_time
            
            # 记录请求完成
            status_code = response.status_code
            logger.info(
                f"✅ {method} {path} - {status_code} - {process_time:.3f}s - IP: {client_ip} - User: {username}"
            )
            
            # 记录活动日志
            await self.log_activity(
                request=request,
                response=response,
                process_time=process_time,
                user_id=user_id,
                username=username,
                client_ip=client_ip,
                user_agent=user_agent
            )
            
            # 添加响应头
            response.headers["X-Process-Time"] = str(process_time)
            response.headers["X-Request-ID"] = self.generate_request_id()
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            
            # 记录错误
            logger.error(
                f"❌ {method} {path} - ERROR: {str(e)} - {process_time:.3f}s - IP: {client_ip} - User: {username}"
            )
            
            # 记录错误日志
            await self.log_error(
                request=request,
                error=e,
                process_time=process_time,
                user_id=user_id,
                username=username,
                client_ip=client_ip,
                user_agent=user_agent
            )
            
            raise
    
    def get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 返回直连IP
        return request.client.host if request.client else "unknown"
    
    def generate_request_id(self) -> str:
        """生成请求ID"""
        import uuid
        return str(uuid.uuid4())
    
    async def log_activity(
        self,
        request: Request,
        response: Response,
        process_time: float,
        user_id: Optional[int],
        username: str,
        client_ip: str,
        user_agent: str
    ):
        """记录活动日志"""
        try:
            # 确定操作类型
            action_type = self.determine_action_type(request.method, request.url.path)
            if not action_type:
                return
            
            # 确定目标类型
            target_type = self.determine_target_type(request.url.path)
            
            # 构建详情信息
            details = {
                "method": request.method,
                "path": request.url.path,
                "query_params": dict(request.query_params),
                "status_code": response.status_code,
                "process_time": process_time,
                "response_size": response.headers.get("content-length")
            }
            
            # 创建活动日志
            activity_log = ActivityLog(
                action_type=action_type,
                target_type=target_type,
                user_id=user_id,
                username=username,
                ip_address=client_ip,
                user_agent=user_agent,
                details=json.dumps(details),
                result="success" if response.status_code < 400 else "error",
                log_level=LogLevel.INFO if response.status_code < 400 else LogLevel.WARNING
            )
            
            # 保存到数据库
            db = next(get_db())
            try:
                db.add(activity_log)
                db.commit()
            except Exception as e:
                logger.error(f"保存活动日志失败: {e}")
                db.rollback()
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"记录活动日志失败: {e}")
    
    async def log_error(
        self,
        request: Request,
        error: Exception,
        process_time: float,
        user_id: Optional[int],
        username: str,
        client_ip: str,
        user_agent: str
    ):
        """记录错误日志"""
        try:
            # 构建错误详情
            details = {
                "method": request.method,
                "path": request.url.path,
                "query_params": dict(request.query_params),
                "error_type": type(error).__name__,
                "error_message": str(error),
                "process_time": process_time
            }
            
            # 创建活动日志
            activity_log = ActivityLog(
                action_type=ActionType.VIEW,  # 默认为查看操作
                target_type=TargetType.SYSTEM,
                user_id=user_id,
                username=username,
                ip_address=client_ip,
                user_agent=user_agent,
                details=json.dumps(details),
                result="error",
                error_message=str(error),
                log_level=LogLevel.ERROR
            )
            
            # 保存到数据库
            db = next(get_db())
            try:
                db.add(activity_log)
                db.commit()
            except Exception as e:
                logger.error(f"保存错误日志失败: {e}")
                db.rollback()
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"记录错误日志失败: {e}")
    
    def determine_action_type(self, method: str, path: str) -> Optional[ActionType]:
        """确定操作类型"""
        # 登录相关
        if "/auth/login" in path:
            return ActionType.LOGIN
        elif "/auth/logout" in path:
            return ActionType.LOGOUT
        
        # 文件相关
        elif "/files" in path:
            if method == "POST":
                return ActionType.UPLOAD
            elif method == "GET" and "/download" in path:
                return ActionType.DOWNLOAD
            elif method == "GET":
                return ActionType.VIEW
            elif method == "DELETE":
                return ActionType.DELETE
            elif method == "PUT" or method == "PATCH":
                return ActionType.EDIT
        
        # 搜索相关
        elif "/search" in path:
            return ActionType.SEARCH
        
        # 用户管理相关
        elif "/users" in path:
            if method == "POST":
                return ActionType.USER_CREATE
            elif method == "DELETE":
                return ActionType.USER_DELETE
            elif method == "PUT" or method == "PATCH":
                return ActionType.USER_UPDATE
            elif method == "GET":
                return ActionType.VIEW
        
        # 系统配置相关
        elif "/admin" in path or "/system" in path:
            return ActionType.SYSTEM_CONFIG
        
        # 默认为查看操作
        elif method == "GET":
            return ActionType.VIEW
        
        return None
    
    def determine_target_type(self, path: str) -> TargetType:
        """确定目标类型"""
        if "/files" in path:
            return TargetType.FILE
        elif "/folders" in path:
            return TargetType.FOLDER
        elif "/users" in path:
            return TargetType.USER
        elif "/permissions" in path:
            return TargetType.PERMISSION
        else:
            return TargetType.SYSTEM
