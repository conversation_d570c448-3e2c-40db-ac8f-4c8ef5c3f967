# -*- coding: utf-8 -*-
"""
Database Connection and Session Management
数据库连接和会话管理
"""
from typing import Generator
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import redis
from app.config import get_settings

settings = get_settings()

# 创建数据库引擎
engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.DATABASE_ECHO,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=20,
    max_overflow=30
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()
metadata = MetaData()

# Redis连接
redis_client = None


def init_redis():
    """Initialize Redis Connection"""
    global redis_client
    redis_client = redis.Redis.from_url(
        settings.REDIS_URL,
        password=settings.REDIS_PASSWORD,
        decode_responses=True
    )


def get_redis() -> redis.Redis:
    """Get Redis Connection"""
    if redis_client is None:
        init_redis()
    return redis_client


def get_db() -> Generator:
    """Get Database Session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def init_database():
    """Initialize Database"""
    # Import all models to ensure they are registered
    from app.models import user, file, permission, activity_log, folder, system_config
    
    # Create all tables
    Base.metadata.create_all(bind=engine)


def close_database():
    """Close Database Connection"""
    engine.dispose()


# Database health check
def check_database_health() -> bool:
    """Check Database Connection Health"""
    try:
        with SessionLocal() as session:
            session.execute("SELECT 1")
            return True
    except Exception:
        return False


# Redis health check
def check_redis_health() -> bool:
    """Check Redis Connection Health"""
    try:
        redis_client = get_redis()
        redis_client.ping()
        return True
    except Exception:
        return False


# Cache key generator
class CacheKeys:
    """Cache Key Generator"""
    
    @staticmethod
    def user_session(user_id: int) -> str:
        return f"user:session:{user_id}"
    
    @staticmethod
    def user_permissions(user_id: int) -> str:
        return f"user:permissions:{user_id}"
    
    @staticmethod
    def file_info(file_id: int) -> str:
        return f"file:info:{file_id}"
    
    @staticmethod
    def search_results(query: str, page: int) -> str:
        return f"search:results:{hash(query)}:{page}"
    
    @staticmethod
    def user_login_attempts(username: str) -> str:
        return f"login:attempts:{username}"
    
    @staticmethod
    def download_count(file_id: int) -> str:
        return f"download:count:{file_id}"
    
    @staticmethod
    def system_stats() -> str:
        return "system:stats"


# Cache manager helper class
class CacheManager:
    """Cache Manager"""
    
    def __init__(self):
        self.redis_client = None
    
    def get_client(self):
        if self.redis_client is None:
            self.redis_client = get_redis()
        return self.redis_client
    
    def set(self, key: str, value: str, expire: int = 3600):
        """Set Cache"""
        client = self.get_client()
        client.setex(key, expire, value)
    
    def get(self, key: str) -> str:
        """Get Cache"""
        client = self.get_client()
        return client.get(key)
    
    def delete(self, key: str):
        """Delete Cache"""
        client = self.get_client()
        client.delete(key)
    
    def exists(self, key: str) -> bool:
        """Check if Cache Exists"""
        client = self.get_client()
        return client.exists(key)
    
    def increment(self, key: str, amount: int = 1) -> int:
        """Increment Counter"""
        client = self.get_client()
        return client.incrby(key, amount)
    
    def expire(self, key: str, seconds: int):
        """Set Expiration Time"""
        client = self.get_client()
        client.expire(key, seconds)


# Create global cache manager instance
cache_manager = CacheManager()
